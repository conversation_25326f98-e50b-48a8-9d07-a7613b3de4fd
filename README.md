# Circuit Data Library

A library (or database?) of quantum circuits as well as their measurement outcomes. It should contain three things:
1. A list of circuits, in QASM format
2. A list of measurement outcomes for each circuit, in json format
3. A file for device benchmarking data, such as parameters for noise models.


Initial thought of the data structure:
```
device_vendor-device_name
├───experiment1
|   ├───circuit1.qasm
|   ├───circuit1_measurement.json
|   ├───circuit2.qasm
|   ├───circuit2_measurement.json
|   ├───...
|   ├───README.md
|   └───device_parameters1.json
├───experiment2
|   ├───circuit1.qasm
|   ├───circuit1_measurement.json
|   ├───circuit2.qasm
|   ├───circuit2_measurement.json
|   ├───...
|   ├───README.md
|   └───device_parameters2.json
├───...
└───README.md
```

For circuit measurement data, it is like
```
{
binary_basis: count_number
}
```
For example,
```
{
    "000110111000": 1, 
    "011000111100": 1, 
    "011010011100": 2, 
    "011011011100": 3, 
    "100000111100": 3, 
    "100001111000": 2,
    ...
}
```

The `README.md` in each experiment may contain some information for this experiment.