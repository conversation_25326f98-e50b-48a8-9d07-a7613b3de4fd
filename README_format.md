# devicelib

A Library Collecting the Configurations of Existing NISQ Devices

TODO: organize the formats for flies in `unprocessed_json`

## IMPORTANT Change from the old format

`cx_coupling` or `zz_coupling` has been changed to `coupling`

## File Structure

`backend_inputs_XXX` folders contains configuration files from Qiskit Runtime Repo (`qiskit_rt`), MQT bench ('mqt'), and some our own old test ('prevtest'). All files inside those three folders are processed by correspinding python scripts to folder `config_outputs`.

`config_outputs_fresh` contains some newly generated configuration using `fetch_ibm_device.py` from Qiskit Runtime Could service.

`ibm_plots.ipynb` demonstrate how the relation between machine online time/configuration update time and T1/T2/two-qubit gate error rate/two-qubit gate length. The plots are saved in folder `plots`.

## Data Structure

The following items are based on IBM devices.

```
'data_source': str, where the data comes from, such as 'qiskit_ibm_runtime_repo'
'name': str, device name, such as 'ibmq_athens'
'version': str, backend version, such as '1.3.13'
'num_qubits': int, number of qubits, such as 5
'last_update_date': str, in the format like '2021-03-15T14:10:39-04:00'
'T1': dict {str: float}, second (s), T1 of each qubit, such as {'0': 6.3e-05, '1': 7.3e-05}
'T2': dict {str: float}, second (s), T2 of each qubit, such as {'0': 6.3e-05, '1': 7.3e-05} 
'freq': dict {str: float}, Hz, frequency of each qubit, such as {'0': 5267216864.1, '1': 5175383639.2} 
'readout_length': dict {str: float}, second (s), readout duration of each qubit, such as {'0': 6.3e-06, '1': 7.3e-06} 
'prob_meas0_prep1': dict {str: float}, probability of measure |0> but get 1 for each qubit, such as {'0': 0.018, '1': 0.034} 
'prob_meas1_prep0': dict {str: float}, probability of measure |1> but get 0 for each qubit, such as {'0': 0.018, '1': 0.034} 
'anharmonicity': dict {str: float}, Hz, anharmonicity of each qubit, such as {'0': -336102370.1, '1': -332545044.1}  
'gate_errs': dict {str: float}, gate error rate of each (basis gate,qubit(s)) combination, such as {'sx0': 0.000377, 'sx1': 0.0003, 'cx1_0': 0.0111, 'cx0_1': 0.01111}
'gate_lens': dict {str: float}, gate error rate of each (basis gate,qubit(s)) combination, such as {'sx0': 3.5555e-08, 'sx1': 3.5555e-08, 'cx1_0': 4.124e-07, 'cx0_1': 3.7688e-07} 
'coupling': array [str], how qubits are connected, such as ['4_3', '3_4', '2_3', '3_2', '1_2', '2_1', '1_0', '0_1']
'basis_gates': array [str], basis gate set, such as ['id', 'rz', 'sx', 'x', 'cx', 'reset']. NOTE: do not record `barrier` and `measure`
'processor': dict {'vendor': str, 'family': str, 'revision': ste}, processor type, such as {'vendor': 'IBM', 'family': 'Falcon', 'revision': '4L'} 
'online_date': str, in the format like '2020-03-13T04:00:00+00:00'
```

## Missing Data Handling

Missing information caused by no API for old Qiskit version and no records from third-party calibration files
1. Processor type `processor`: fill `{family: 'Unknown', 'revesion':0, 'Vendor':(provided from the user input)}`
2. Anharmonicity `anharmonicity` and version `version`: fill `None`
3. Measurement error rates `prob_meas0_prep1` and `prob_meas1_prep0`: fill the value that REPRESENTS READOUT ERROR
4. Gate error `gate_errs` and gate length `gate_lens`: fill `None`
5. Last update time `last_update_date` and online time `online_date`: fill `None`
6. Any other entry: fill `None`


## Data Converter for Each Data Source

### Data Source: Qiskit Runtime Fakebackend Repo

All configuration files are already generated in the folder `config_outputs`. Below are the steps if there are more backends to add. No dependecy on any extra packages besides Python.
Units are recorded in the input files

1. Use the files from [Qiskit Runtime fake providers](https://github.com/Qiskit/qiskit-ibm-runtime/tree/d807c9ea23b5307d6172ea1990106a06a01bcba8/qiskit_ibm_runtime/fake_provider/backends)
  - Or, inside folder `backend_inputs`, put at least a json file generated from [BackendProperties](https://docs.quantum.ibm.com/api/qiskit/qiskit.providers.models.BackendProperties) in the nested folder named by the backend name. It is better to also have a json file for [BackendConfiguration](https://docs.quantum.ibm.com/api/qiskit/qiskit.providers.models.BackendConfiguration)
2. Create a folder `config_outputs`.
3. Run `config_reader_qiskit_rt.py`.


### Data Source: MQT Bench Repo

WARNING: Famly info is unchecked for IonQ, IQM, OQC, Quantinuum, Rigetti

Data files are from [MQT Bench calibration folder](https://github.com/cda-tum/mqt-bench/tree/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/calibration_files)
Units are recorded in the comments in [MQT Bench devices folder](https://github.com/cda-tum/mqt-bench/tree/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices)

#### Data Clean-up for IBMQ devices
1. Only has the readout fidelity without differentiating measure 0 and 1, it is recorded as `prob_meas0_prep1` and `prob_meas1_prep0`
2. Not have processor information, used the same data from [Qiskit Runtime fake providers](https://github.com/Qiskit/qiskit-ibm-runtime/tree/d807c9ea23b5307d6172ea1990106a06a01bcba8/qiskit_ibm_runtime/fake_provider/backends)
3. Not have gate length for 1-qubit gates and calibration time



#### Data Clean-up for IonQ devices
See [IonQ Guide](https://docs.ionq.com/#tag/characterizations)
1. `RZ` is always perfect and instantaneous according [MQT's reconstruction](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/ionq.py)
2. Use `1q` and `2q` `timing` for all 1- and 2-qubit gate length
3. Using `1-Mean Fidelity` for all error rates, use `mean spam fidelity` for readout error rates 
4. `timing` in IonQ devices is seconds [(right, T1 is 100 seconds)](https://ionq.com/resources/ionq-aria-practical-performance)

#### Data Clean-up for IQM devices
See [IQM Guide](https://docs.iqm.com/#tag/characterizations)
1. Unit for time is ns, see [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/iqm.py)
2. CZ error rates are the same for both directions


#### Data Clean-up for OQC devices

1. Two-qubit gates `ECR` only has ONE-DIRECTION information, including coupling and error rates
2. Unit for time is microseconds, see [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/oqc.py) 
3. Use `fRB` as the single-qubit gate fidelity for all single-gate qubit
4. Compute all error rates as `1 - Fidelity`
5. No readout length
6. ??? `RZ` is not perfect????, see [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/oqc.py) 

#### Data Clean-up for Quantinuum devices

1. No any gate length, nor T1, T2 information


#### Data Clean-up for Rigetti devices

1. No any gate length
2. Missing some two-qubit gate fidelity, use the average value to fill in, same as [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/rigetti.py)
3. Compute all error rates as `1 - Fidelity`
4. Use `f1QRB` not `f1Q_simultaneous_RB` for gate fidelity and `fActiveReset` for reset gate fidelity
5. Two-qubit gate error is symmetric, see [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/rigetti.py#L197)
6. ??? `RZ` is not perfect????, see [MQT repo](https://github.com/cda-tum/mqt-bench/blob/254ab092e80138d3afdc69f0c7e95c75920ad91f/src/mqt/bench/devices/rigetti.py) 


### Data Source: Previous Json Files Generated from Old Codes

`data_source` is added accordingly. `last_update_date` and `anharmonicity` are set to `None`. `processor` is added based on the data from Qiskit Runtime Fakebackend and speculated information. `online_date` is added based on the data from Qiskit Runtime Fakebackend or `None`

The files in `unprocessed_json/dummy_ibm`, `backend_config.json`, `example_config.json`, `h1_1.json`, `h1_2.json` are unprocessed. Their data sources need a further confirmation.



### Data Source: IQM websit
See comments in `fetch_iqm_device.py` for detailed mapping. The tricky thing is, IQM does not provide API access of benchmarking data, so the data is manually downloaded from the website and put in `backend_inputs_iqm` folder. The trickier thing is, the `key` in benchmarking data files are different for both machines, possibly due to different architecture. So the future maintainance is painful and requires a lot of attention.