import os

def rename_qasm_files(directory):
    """
    Rename .qasm files by removing the first '-' and 'QuantinuumBackend-final' suffix
    """
    for root, dirs, files in os.walk(directory):
        for filename in files:
            if filename.endswith('.qasm'):
                # Get the full path
                old_path = os.path.join(root, filename)
                
                # Remove '-QuantinuumBackend-final' suffix
                # new_name = filename.replace('-', '', 1)  # Remove first '-'
                nf_str, temp_str = filename.split('-')
                temp_str
                new_name = f"{nf_str}-{pauli_str}-itr3.qasm"
                
                # Create new path
                new_path = os.path.join(root, new_name)
                
                # Rename file
                os.rename(old_path, new_path)
                print(f"Renamed: {filename} -> {new_name}")

if __name__ == "__main__":
    # Get the directory where the script is located
    # Get directory from command line argument
    current_dir = "./IONQ Forte-1/QVQE_Benezne_ccpVTZ_nf135"
    rename_qasm_files(current_dir)
