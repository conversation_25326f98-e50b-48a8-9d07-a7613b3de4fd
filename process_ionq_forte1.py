#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to process IONQ Forte-1 characterization data into the standard format
following the data structure specified in README_format.md
"""

import json
import datetime
from typing import Dict, Any


def convert_timestamp_to_iso(timestamp: int) -> str:
    """Convert Unix timestamp to ISO format string"""
    dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)
    return dt.isoformat()


def create_qubit_dict(num_qubits: int, value: float) -> Dict[str, float]:
    """Create a dictionary with qubit indices as keys and the same value for all qubits"""
    return {str(i): value for i in range(num_qubits)}


def create_gate_errors_dict(num_qubits: int, connectivity: list, fidelity: dict) -> Dict[str, float]:
    """Create gate errors dictionary based on IONQ fidelity data"""
    gate_errs = {}
    
    # Single-qubit gate errors (RZ is perfect, RY/RX use 1q fidelity)
    single_qubit_error = 1 - fidelity["1q"]["mean"]
    for i in range(num_qubits):
        gate_errs[f"rz{i}"] = 0.0  # RZ is perfect for IONQ
        gate_errs[f"ry{i}"] = single_qubit_error
        gate_errs[f"rx{i}"] = single_qubit_error
        gate_errs[f"reset{i}"] = None  # No reset error data available
    
    # Two-qubit gate errors (RXX gates)
    two_qubit_error = 1 - fidelity["2q"]["mean"]
    for connection in connectivity:
        q1, q2 = connection
        gate_errs[f"rxx{q1}_{q2}"] = two_qubit_error
    
    return gate_errs


def create_gate_lengths_dict(num_qubits: int, connectivity: list, timing: dict) -> Dict[str, float]:
    """Create gate lengths dictionary based on IONQ timing data"""
    gate_lens = {}
    
    # Single-qubit gate lengths
    for i in range(num_qubits):
        gate_lens[f"rz{i}"] = 0.0  # RZ is instantaneous
        gate_lens[f"ry{i}"] = timing["1q"]
        gate_lens[f"rx{i}"] = timing["1q"]
        gate_lens[f"reset{i}"] = timing["reset"]
    
    # Two-qubit gate lengths (RXX gates)
    for connection in connectivity:
        q1, q2 = connection
        gate_lens[f"rxx{q1}_{q2}"] = timing["2q"]
    
    return gate_lens


def create_coupling_list(connectivity: list) -> list:
    """Convert connectivity format from [[q1,q2],...] to ["q1_q2",...]"""
    return [f"{q1}_{q2}" for q1, q2 in connectivity]


def calculate_readout_errors(spam_fidelity: float) -> float:
    """Calculate readout error from SPAM fidelity"""
    # SPAM error represents both prob_meas0_prep1 and prob_meas1_prep0
    return 1 - spam_fidelity


def process_ionq_forte1_data(input_file: str, output_file: str) -> None:
    """
    Process IONQ Forte-1 characterization data and convert to standard format
    
    Args:
        input_file: Path to input JSON file
        output_file: Path to output JSON file
    """
    
    # Load input data
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    num_qubits = data["qubits"]
    connectivity = data["connectivity"]
    fidelity = data["fidelity"]
    timing = data["timing"]
    
    # Calculate readout error from SPAM fidelity
    readout_error = calculate_readout_errors(fidelity["spam"]["mean"])
    
    # Create output data structure
    output_data = {
        "data_source": "ionq_characterization",
        "name": "ionq_forte1",
        "version": None,
        "num_qubits": num_qubits,
        "last_update_date": convert_timestamp_to_iso(data["date"]),
        "T1": create_qubit_dict(num_qubits, timing["t1"]),
        "T2": create_qubit_dict(num_qubits, timing["t2"]),
        "freq": None,  # No frequency data available
        "readout_length": create_qubit_dict(num_qubits, timing["readout"]),
        "prob_meas0_prep1": create_qubit_dict(num_qubits, readout_error),
        "prob_meas1_prep0": create_qubit_dict(num_qubits, readout_error),
        "anharmonicity": None,  # No anharmonicity data available
        "gate_errs": create_gate_errors_dict(num_qubits, connectivity, fidelity),
        "gate_lens": create_gate_lengths_dict(num_qubits, connectivity, timing),
        "coupling": create_coupling_list(connectivity),
        "basis_gates": ["rxx", "rz", "ry", "rx", "reset"],
        "processor": {
            "vendor": "IonQ",
            "family": "Forte",
            "revision": "1"
        },
        "online_date": None  # No online date information available
    }
    
    # Save output data
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"Successfully converted {input_file} to {output_file}")
    print(f"Device: {output_data['name']} with {output_data['num_qubits']} qubits")
    print(f"Last update: {output_data['last_update_date']}")


if __name__ == "__main__":
    input_file = "IONQ Forte-1/QVQE_Benezne_ccpVTZ_nf135/qpu.forte-1-characterization-data.json"
    output_file = "ionq_forte1_n36.json"
    
    process_ionq_forte1_data(input_file, output_file)
