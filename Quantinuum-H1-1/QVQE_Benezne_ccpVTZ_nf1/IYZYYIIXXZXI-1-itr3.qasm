OPENQASM 3.0;
include "stdgates.inc";
gate r(p0, p1) _gate_q_0 {
  u3(p0, p1 - pi/2, pi/2 - p1) _gate_q_0;
}
gate rzz(p0) _gate_q_0, _gate_q_1 {
  cx _gate_q_0, _gate_q_1;
  rz(p0) _gate_q_1;
  cx _gate_q_0, _gate_q_1;
}
bit[12] meas;
qubit[12] q;
r(pi, 0) q[0];
r(pi/2, 3*pi/2) q[1];
r(pi/2, 3*pi/2) q[2];
rz(pi) q[3];
rz(pi) q[4];
r(pi, 0) q[6];
r(pi/2, pi) q[7];
r(pi/2, pi) q[8];
rz(pi) q[9];
rz(pi) q[10];
rz(pi) q[1];
rz(pi) q[2];
r(pi/2, pi/2) q[3];
r(pi/2, pi/2) q[4];
rz(pi) q[7];
rz(pi) q[8];
r(pi/2, pi/2) q[9];
r(pi/2, pi/2) q[10];
r(3*pi/2, pi/2) q[1];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(pi) q[4];
r(3*pi/2, pi/2) q[7];
r(3*pi/2, pi/2) q[8];
rz(pi) q[9];
rz(pi) q[10];
r(3*pi/2, pi/2) q[3];
r(3*pi/2, pi/2) q[4];
rzz(pi/2) q[9], q[8];
rz(pi/2) q[8];
rz(3*pi/2) q[9];
r(3*pi/2, pi/2) q[8];
rz(pi) q[9];
rz(pi) q[8];
rzz(pi/2) q[8], q[3];
rz(pi/2) q[3];
rz(3*pi/2) q[8];
r(3*pi/2, pi/2) q[3];
rz(pi) q[8];
rz(pi) q[3];
rzz(pi/2) q[3], q[2];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(0.152126622397136) q[2];
rz(pi) q[2];
r(3*pi/2, pi/2) q[2];
rzz(pi/2) q[3], q[2];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(pi) q[2];
r(3*pi/2, pi/2) q[3];
r(pi/2, pi/2) q[2];
rzz(pi/2) q[8], q[3];
rz(pi) q[2];
rz(pi/2) q[3];
rz(3*pi/2) q[8];
r(3*pi/2, pi/2) q[2];
r(3*pi/2, pi/2) q[3];
rz(pi) q[8];
rz(pi) q[3];
r(3*pi/2, pi/2) q[8];
r(pi/2, pi/2) q[3];
rzz(pi/2) q[9], q[8];
rz(pi) q[3];
rz(pi/2) q[8];
rz(3*pi/2) q[9];
r(3*pi/2, pi/2) q[3];
r(3*pi/2, pi/2) q[8];
rz(pi) q[9];
r(7*pi/2, 0) q[8];
r(pi/2, pi/2) q[9];
rz(pi) q[8];
rz(pi) q[9];
r(3*pi/2, pi/2) q[8];
r(3*pi/2, pi/2) q[9];
rzz(pi/2) q[10], q[9];
rz(pi/2) q[9];
rz(3*pi/2) q[10];
r(3*pi/2, pi/2) q[9];
rz(pi) q[10];
rz(pi) q[9];
rzz(pi/2) q[9], q[8];
rz(pi/2) q[8];
rz(3*pi/2) q[9];
r(3*pi/2, pi/2) q[8];
r(0.724905077241367, pi) q[9];
rz(pi) q[8];
rz(pi) q[9];
rzz(pi/2) q[8], q[7];
r(3*pi/2, pi/2) q[9];
rz(pi/2) q[7];
rz(3*pi/2) q[8];
r(3*pi/2, pi/2) q[7];
rz(pi) q[8];
rz(pi) q[7];
rzz(pi/2) q[7], q[4];
rz(pi/2) q[4];
rz(3*pi/2) q[7];
r(3*pi/2, pi/2) q[4];
rz(pi) q[7];
rz(pi) q[4];
rzz(pi/2) q[4], q[3];
rz(pi/2) q[3];
rz(3*pi/2) q[4];
r(3*pi/2, pi/2) q[3];
rz(pi) q[4];
rz(pi) q[3];
rzz(pi/2) q[3], q[2];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(pi) q[2];
rzz(pi/2) q[2], q[1];
rz(pi/2) q[1];
rz(3*pi/2) q[2];
r(3*pi/2, pi/2) q[1];
rz(pi) q[2];
rz(0.152655712753207) q[1];
rz(pi) q[1];
r(3*pi/2, pi/2) q[1];
rzz(pi/2) q[2], q[1];
rz(pi/2) q[1];
rz(3*pi/2) q[2];
r(3*pi/2, pi/2) q[1];
rz(pi) q[2];
rz(7*pi/2) q[1];
r(3*pi/2, pi/2) q[2];
r(pi/2, pi/2) q[1];
rzz(pi/2) q[3], q[2];
rz(pi) q[1];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
r(3*pi/2, pi/2) q[1];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
r(pi/2, 0) q[2];
r(3*pi/2, pi/2) q[3];
rz(pi) q[2];
rzz(pi/2) q[4], q[3];
r(3*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(3*pi/2) q[4];
r(3*pi/2, pi/2) q[3];
rz(pi) q[4];
r(pi/2, 0) q[3];
r(3*pi/2, pi/2) q[4];
rz(pi) q[3];
rzz(pi/2) q[7], q[4];
r(3*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(3*pi/2) q[7];
r(3*pi/2, pi/2) q[4];
rz(pi) q[7];
rz(pi) q[4];
r(3*pi/2, pi/2) q[7];
rzz(pi/2) q[4], q[3];
rzz(pi/2) q[8], q[7];
rz(pi/2) q[3];
rz(3*pi/2) q[4];
rz(pi/2) q[7];
rz(3*pi/2) q[8];
r(3*pi/2, pi/2) q[3];
rz(pi) q[4];
r(3*pi/2, pi/2) q[7];
rz(7.138022179330491) q[8];
rz(pi) q[3];
rz(pi) q[7];
r(pi/2, 5.567225852535594) q[8];
rzz(pi/2) q[3], q[2];
r(3*pi/2, pi/2) q[7];
rz(pi) q[8];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
rzz(pi/2) q[8], q[9];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(3*pi/2) q[8];
rz(pi/2) q[9];
rz(pi) q[2];
rz(2.28675578143889) q[8];
r(3*pi/2, pi/2) q[9];
rzz(pi/2) q[2], q[1];
r(pi/2, pi/2) q[8];
r(2.29570140403626, 0) q[9];
rz(pi/2) q[1];
rz(3*pi/2) q[2];
rz(pi) q[8];
rz(pi) q[9];
r(3*pi/2, pi/2) q[1];
rz(pi) q[2];
rzz(pi/2) q[8], q[7];
rz(0.11378906482779702) q[1];
rz(pi/2) q[7];
rz(3*pi/2) q[8];
rz(pi) q[1];
r(3*pi/2, pi/2) q[7];
rz(pi) q[8];
r(3*pi/2, pi/2) q[1];
rz(0.113797899918679) q[7];
rzz(pi/2) q[2], q[1];
rz(pi) q[7];
rz(pi/2) q[1];
rz(3*pi/2) q[2];
r(3*pi/2, pi/2) q[7];
r(3*pi/2, pi/2) q[1];
rz(pi) q[2];
rzz(pi/2) q[8], q[7];
r(7*pi/2, 0) q[1];
r(3*pi/2, pi/2) q[2];
rz(pi/2) q[7];
rz(3*pi/2) q[8];
rz(7*pi/2) q[1];
rzz(pi/2) q[3], q[2];
r(3*pi/2, pi/2) q[7];
rz(pi) q[8];
rz(pi) q[1];
rz(pi/2) q[2];
rz(3*pi/2) q[3];
r(7*pi/2, 0) q[7];
r(3*pi/2, pi/2) q[8];
r(pi/2, pi/2) q[1];
r(3*pi/2, pi/2) q[2];
rz(pi) q[3];
rz(pi) q[7];
rzz(pi/2) q[9], q[8];
r(7*pi/2, 0) q[2];
r(3*pi/2, pi/2) q[3];
r(pi/2, pi/2) q[7];
rz(pi/2) q[8];
rz(3*pi/2) q[9];
rzz(pi/2) q[4], q[3];
r(3*pi/2, pi/2) q[8];
rz(pi) q[9];
rz(pi/2) q[3];
rz(3*pi/2) q[4];
r(7*pi/2, 0) q[8];
r(3*pi/2, pi/2) q[9];
r(3*pi/2, pi/2) q[3];
rz(pi) q[4];
rz(pi) q[8];
rzz(pi/2) q[10], q[9];
r(7*pi/2, 0) q[3];
r(pi/2, pi/2) q[4];
r(pi/2, pi/2) q[8];
rz(pi/2) q[9];
rz(3*pi/2) q[10];
rz(7*pi/2) q[3];
rz(7*pi/2) q[4];
r(3*pi/2, pi/2) q[9];
rz(pi) q[10];
rz(pi) q[3];
rz(pi) q[4];
r(7*pi/2, 0) q[9];
r(pi/2, pi/2) q[10];
r(pi/2, pi/2) q[3];
r(pi/2, pi/2) q[4];
rz(pi) q[10];
r(pi/2, pi/2) q[10];
barrier q[0], q[1], q[2], q[3], q[4], q[5], q[6], q[7], q[8], q[9], q[10], q[11];
meas[0] = measure q[0];
meas[1] = measure q[1];
meas[2] = measure q[2];
meas[3] = measure q[3];
meas[4] = measure q[4];
meas[5] = measure q[5];
meas[6] = measure q[6];
meas[7] = measure q[7];
meas[8] = measure q[8];
meas[9] = measure q[9];
meas[10] = measure q[10];
meas[11] = measure q[11];
