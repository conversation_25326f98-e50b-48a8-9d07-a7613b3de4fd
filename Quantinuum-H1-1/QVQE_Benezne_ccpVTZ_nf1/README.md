## Experiment Information

One-shot evaluations of circuits from a selected iteration in Qubit-ADAPT-VQE.

- Molecule: Benezene
- Basis Set: ccpVTZ
- Number of qubits: 12
- Selected Iteration: 3 (starting from 0, so 4 layers of ansatz)
- Backend: Quantinuum H1-1
- Shots for each circuit: 1024

Circuit naming convention `{<PERSON>i commutator}-{noise factor}-itr{iteration}.qasm`, where <PERSON><PERSON> commutator is the Pauli string used in the grouping of the Hamiltonian. Noise factor represents the degree of amplified noise by gate insertion. I only duplicated the 2-qubit basis gates in the last 2 layers of ansatz.

## Noise Parameters

I have not get a good script to extract the noise parameters from Quantinuum. I have to manually copy them from the Quantinuum backend page. 
```
Field	Value
Date	2025-05-02
One Qubit Gate Error
P1	1.80e-5
P1 Unc	2.93e-6
Two Qubit Gate Error
P2	9.73e-4
P2 Unc	6.23e-5
Spam Error
P Meas 0	1.22e-3
P Meas 0 Unc	1.05e-4
P Meas 1	3.43e-3
P Meas 1 Unc	1.76e-4
Crosstalk Error
P Crosstalk Meas	1.45e-5
P Crosstalk Meas Unc	1.02e-6
Memory Error
Memory Error	2.22e-4
Memory Error Unc	9.16e-5
```