OPENQASM 3.0;
include "stdgates.inc";
gate r(p0, p1) _gate_q_0 {
  u3(p0, p1 - pi/2, pi/2 - p1) _gate_q_0;
}
gate rzz(p0) _gate_q_0, _gate_q_1 {
  cx _gate_q_0, _gate_q_1;
  rz(p0) _gate_q_1;
  cx _gate_q_0, _gate_q_1;
}
bit[12] meas;
qubit[12] q;
r(pi, 0) q[0];
r(pi, 0) q[1];
r(pi, 0) q[2];
r(pi/2, 0) q[3];
rz(pi) q[4];
r(pi, 0) q[6];
r(pi/2, 3*pi/2) q[7];
r(pi/2, 3*pi/2) q[8];
r(pi/2, 0) q[9];
rz(pi) q[10];
rz(7*pi/2) q[11];
rz(pi) q[0];
rz(pi) q[1];
rz(pi) q[2];
rz(7*pi/2) q[3];
r(pi/2, pi/2) q[4];
rz(pi) q[6];
rz(7*pi/2) q[7];
rz(7*pi/2) q[8];
rz(7*pi/2) q[9];
r(pi/2, pi/2) q[10];
rz(pi) q[11];
r(pi/2, pi/2) q[0];
r(pi/2, pi/2) q[1];
r(pi/2, pi/2) q[2];
r(5*pi/2, 0) q[3];
rz(pi/2) q[4];
r(pi/2, pi/2) q[6];
r(5*pi/2, 0) q[7];
r(5*pi/2, 0) q[8];
r(5*pi/2, 0) q[9];
rz(pi/2) q[10];
r(pi/2, pi/2) q[11];
rz(7*pi/2) q[0];
rz(7*pi/2) q[1];
rz(7*pi/2) q[2];
rzz(pi/2) q[4], q[3];
rzz(pi/2) q[10], q[9];
r(5*pi/2, 0) q[0];
r(5*pi/2, 0) q[1];
r(5*pi/2, 0) q[2];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
r(7*pi/2, pi/2) q[9];
rz(pi/2) q[10];
rz(7*pi/2) q[3];
rz(pi/2) q[9];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[9], q[8];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[8];
rz(pi/2) q[9];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(pi/2) q[8];
rz(7*pi/2) q[3];
rzz(pi/2) q[8], q[7];
r(5*pi/2, 0) q[3];
r(7*pi/2, pi/2) q[7];
rz(pi/2) q[8];
rzz(pi/2) q[4], q[3];
rz(12.422249690818555) q[7];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(7*pi/2) q[7];
rz(7*pi/2) q[3];
r(5*pi/2, 0) q[7];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[8], q[7];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[7];
rz(7*pi/2) q[8];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(pi) q[7];
r(5*pi/2, 0) q[8];
rz(7*pi/2) q[3];
r(pi/2, pi/2) q[7];
rzz(pi/2) q[9], q[8];
r(5*pi/2, 0) q[3];
r(7*pi/2, pi/2) q[8];
rz(7*pi/2) q[9];
rzz(pi/2) q[4], q[3];
rz(pi) q[8];
r(5*pi/2, 0) q[9];
r(7*pi/2, pi/2) q[3];
r(2.55804043130222, 0) q[4];
r(pi/2, pi/2) q[8];
rzz(pi/2) q[10], q[9];
rz(pi/2) q[3];
rz(7*pi/2) q[4];
r(7*pi/2, pi/2) q[9];
rz(pi) q[10];
rzz(pi/2) q[3], q[2];
r(5*pi/2, 0) q[4];
r(7*pi/2, 0) q[9];
r(pi/2, pi/2) q[10];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(pi/2) q[2];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(12.42217772890704) q[1];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(7*pi/2) q[2];
rz(pi) q[1];
r(5*pi/2, 0) q[2];
r(pi/2, pi/2) q[1];
rzz(pi/2) q[3], q[2];
rz(7*pi/2) q[1];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
r(5*pi/2, 0) q[1];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(6.5706315783473315) q[3];
rz(pi) q[2];
r(pi/2, 4.999835251552432) q[3];
r(pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
rzz(pi/2) q[3], q[4];
r(5*pi/2, 0) q[2];
rz(pi/2) q[3];
r(7*pi/2, pi/2) q[4];
rz(7*pi/2) q[4];
r(5*pi/2, 0) q[4];
rzz(pi/2) q[3], q[4];
rz(pi/2) q[3];
r(7*pi/2, pi/2) q[4];
rz(7*pi/2) q[4];
r(5*pi/2, 0) q[4];
rzz(pi/2) q[3], q[4];
rz(pi/2) q[3];
r(7*pi/2, pi/2) q[4];
rz(7*pi/2) q[4];
r(5*pi/2, 0) q[4];
rzz(pi/2) q[3], q[4];
rz(pi/2) q[3];
r(7*pi/2, pi/2) q[4];
rz(7*pi/2) q[4];
r(5*pi/2, 0) q[4];
rzz(pi/2) q[3], q[4];
rz(2.8541463824220505) q[3];
r(7*pi/2, pi/2) q[4];
r(pi/2, pi/2) q[3];
rz(7*pi/2) q[4];
rz(pi/2) q[3];
r(2.15434854908248, 3*pi/2) q[4];
rzz(pi/2) q[3], q[2];
rz(pi/2) q[4];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(pi/2) q[2];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(pi/2) q[1];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(0.053534367534413506) q[0];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(pi/2) q[1];
rz(7*pi/2) q[0];
r(5*pi/2, 0) q[0];
rzz(pi/2) q[1], q[0];
r(7*pi/2, pi/2) q[0];
rz(7*pi/2) q[1];
rz(pi) q[0];
r(5*pi/2, 0) q[1];
r(pi/2, pi/2) q[0];
rzz(pi/2) q[2], q[1];
rz(pi) q[0];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
r(pi/2, pi/2) q[0];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[1];
rzz(pi/2) q[2], q[1];
r(7*pi/2, pi/2) q[1];
rz(7*pi/2) q[2];
rz(7*pi/2) q[1];
r(5*pi/2, 0) q[2];
rz(pi) q[1];
rzz(pi/2) q[3], q[2];
r(pi/2, pi/2) q[1];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(pi/2) q[3];
rz(7*pi/2) q[2];
r(5*pi/2, 0) q[2];
rzz(pi/2) q[3], q[2];
r(7*pi/2, pi/2) q[2];
rz(7*pi/2) q[3];
rz(pi) q[2];
r(5*pi/2, 0) q[3];
r(pi/2, pi/2) q[2];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(7*pi/2) q[3];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(7*pi/2) q[3];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(7*pi/2) q[3];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[3];
rz(pi/2) q[4];
rz(7*pi/2) q[3];
r(5*pi/2, 0) q[3];
rzz(pi/2) q[4], q[3];
r(7*pi/2, pi/2) q[3];
r(7*pi/2, 0) q[4];
rz(7*pi/2) q[4];
rz(pi) q[4];
r(pi/2, pi/2) q[4];
barrier q[0], q[1], q[2], q[3], q[4], q[5], q[6], q[7], q[8], q[9], q[10], q[11];
meas[0] = measure q[0];
meas[1] = measure q[1];
meas[2] = measure q[2];
meas[3] = measure q[3];
meas[4] = measure q[4];
meas[5] = measure q[5];
meas[6] = measure q[6];
meas[7] = measure q[7];
meas[8] = measure q[8];
meas[9] = measure q[9];
meas[10] = measure q[10];
meas[11] = measure q[11];
