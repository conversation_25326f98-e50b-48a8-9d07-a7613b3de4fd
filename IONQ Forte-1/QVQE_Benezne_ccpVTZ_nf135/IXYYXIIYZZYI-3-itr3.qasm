OPENQASM 2.0;
include "qelib1.inc";
qreg q[12];
creg meas[12];
x q[0];
ry(-pi/2) q[1];
ry(-pi/2) q[2];
h q[3];
h q[4];
x q[6];
rx(-pi/2) q[7];
rx(-pi/2) q[8];
h q[9];
cx q[9],q[8];
cx q[8],q[3];
cx q[3],q[2];
rz(0.15212662239713648) q[2];
cx q[3],q[2];
h q[2];
cx q[8],q[3];
h q[3];
cx q[9],q[8];
sxdg q[8];
h q[9];
h q[10];
cx q[10],q[9];
cx q[9],q[8];
cx q[8],q[7];
cx q[7],q[4];
cx q[4],q[3];
cx q[3],q[2];
cx q[2],q[1];
rz(0.15265571275320708) q[1];
cx q[2],q[1];
h q[1];
cx q[3],q[2];
cx q[4],q[3];
cx q[7],q[4];
h q[4];
cx q[8],q[7];
sxdg q[7];
cx q[9],q[8];
cx q[10],q[9];
h q[10];
sx q[1];
sx q[2];
sx q[3];
h q[4];
cx q[4],q[3];
cx q[4],q[3];
cx q[4],q[3];
cx q[3],q[2];
cx q[3],q[2];
cx q[3],q[2];
cx q[2],q[1];
cx q[2],q[1];
cx q[2],q[1];
rz(0.11378906482779706) q[1];
cx q[2],q[1];
cx q[2],q[1];
cx q[2],q[1];
sxdg q[1];
cx q[3],q[2];
cx q[3],q[2];
cx q[3],q[2];
sxdg q[2];
cx q[4],q[3];
cx q[4],q[3];
cx q[4],q[3];
sxdg q[3];
h q[4];
sx q[7];
sx q[8];
sx q[9];
h q[10];
cx q[10],q[9];
cx q[10],q[9];
cx q[10],q[9];
cx q[9],q[8];
cx q[9],q[8];
cx q[9],q[8];
cx q[8],q[7];
cx q[8],q[7];
cx q[8],q[7];
rz(0.11379789991867921) q[7];
cx q[8],q[7];
cx q[8],q[7];
cx q[8],q[7];
sxdg q[7];
cx q[9],q[8];
cx q[9],q[8];
cx q[9],q[8];
sxdg q[8];
cx q[10],q[9];
cx q[10],q[9];
cx q[10],q[9];
sxdg q[9];
h q[10];
h q[1];
sdg q[2];
h q[2];
sdg q[3];
h q[3];
h q[4];
sdg q[7];
h q[7];
sdg q[10];
h q[10];
barrier q[0],q[1],q[2],q[3],q[4],q[5],q[6],q[7],q[8],q[9],q[10],q[11];
measure q[0] -> meas[0];
measure q[1] -> meas[1];
measure q[2] -> meas[2];
measure q[3] -> meas[3];
measure q[4] -> meas[4];
measure q[5] -> meas[5];
measure q[6] -> meas[6];
measure q[7] -> meas[7];
measure q[8] -> meas[8];
measure q[9] -> meas[9];
measure q[10] -> meas[10];
measure q[11] -> meas[11];
