## Experiment Information

One-shot evaluations of circuits from a selected iteration in Qubit-ADAPT-VQE.

- Molecule: Benezene
- Basis Set: ccpVTZ
- Number of qubits: 12
- Selected Iteration: 3 (starting from 0, so 4 layers of ansatz)
- Backend: IONQ Forte-1
- Shots for each circuit: 1024

Circuit naming convention `{Pauli commutator}-{noise factor}-itr{iteration}.qasm`, where <PERSON><PERSON> commutator is the Pauli string used in the grouping of the Hamiltonian. Noise factor represents the degree of amplified noise by gate insertion. I only duplicated the 2-qubit basis gates in the last 2 layers of ansatz.

## Noise Parameters

See the raw data`qpu.forte-1-characterization-data.json` downloaded from IONQ (June 10, 2025)