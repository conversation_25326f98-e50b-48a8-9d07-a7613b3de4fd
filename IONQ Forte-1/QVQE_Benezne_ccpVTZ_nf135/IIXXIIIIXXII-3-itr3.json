{"000111001111": 67, "001111001011": 66, "001011001111": 70, "001011000111": 53, "000011001111": 51, "000011001011": 67, "000011000111": 63, "001111000111": 59, "000111000011": 57, "000111000111": 53, "001011000011": 63, "001111001111": 59, "001111000011": 53, "000011000011": 51, "000111001011": 47, "001011001011": 40, "011111000111": 4, "001001000111": 3, "010011000111": 3, "000001001111": 3, "010001001011": 3, "010111000011": 3, "010111001011": 3, "000111000001": 3, "010011000011": 2, "000110000011": 2, "001011011111": 2, "001011010011": 2, "011101001111": 2, "011011001111": 2, "000111010011": 2, "000111011111": 2, "011111001011": 2, "001111011011": 2, "000111001001": 2, "001111000101": 2, "010111000111": 2, "010101011111": 2, "000001000111": 2, "001111010111": 2, "011011001011": 2, "010101011011": 2, "001100000011": 1, "011001010001": 1, "010101001011": 1, "001011000110": 1, "011011000011": 1, "000101000111": 1, "010001010101": 1, "011100010101": 1, "001010000011": 1, "000111010111": 1, "001110001011": 1, "011001010101": 1, "000111000110": 1, "010001000111": 1, "010001010111": 1, "000101011011": 1, "001011011011": 1, "001101000011": 1, "011111000011": 1, "000011011001": 1, "001011001110": 1, "000011000001": 1, "000111011011": 1, "001010000111": 1, "001011000001": 1, "001011000101": 1, "010101010001": 1, "010011001011": 1, "011111001111": 1, "010101010101": 1, "001111001110": 1, "001011101111": 1, "001001001011": 1, "010001000011": 1, "011011010001": 1, "010101001111": 1, "001111001001": 1, "011101000111": 1, "000011010011": 1, "001110011101": 1, "000011000010": 1, "010101010011": 1, "001111011101": 1, "011111011101": 1, "010001011111": 1, "001111010011": 1, "001101011011": 1, "010001011011": 1, "001110001111": 1, "000001000011": 1, "000101000011": 1, "011001011111": 1, "011110010110": 1, "011001010111": 1, "010001001101": 1, "000111010101": 1, "000101001011": 1, "011001011101": 1, "001001000011": 1, "001011010111": 1, "000011101011": 1, "010011001111": 1, "010111001111": 1, "001001001111": 1, "011101011111": 1}