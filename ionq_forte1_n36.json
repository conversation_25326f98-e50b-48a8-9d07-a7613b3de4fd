{"data_source": "ionq_characterization", "name": "ionq_forte1", "version": null, "num_qubits": 36, "last_update_date": "2025-06-04T00:00:00+00:00", "T1": {"0": 100, "1": 100, "2": 100, "3": 100, "4": 100, "5": 100, "6": 100, "7": 100, "8": 100, "9": 100, "10": 100, "11": 100, "12": 100, "13": 100, "14": 100, "15": 100, "16": 100, "17": 100, "18": 100, "19": 100, "20": 100, "21": 100, "22": 100, "23": 100, "24": 100, "25": 100, "26": 100, "27": 100, "28": 100, "29": 100, "30": 100, "31": 100, "32": 100, "33": 100, "34": 100, "35": 100}, "T2": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1}, "freq": null, "readout_length": {"0": 0.00015, "1": 0.00015, "2": 0.00015, "3": 0.00015, "4": 0.00015, "5": 0.00015, "6": 0.00015, "7": 0.00015, "8": 0.00015, "9": 0.00015, "10": 0.00015, "11": 0.00015, "12": 0.00015, "13": 0.00015, "14": 0.00015, "15": 0.00015, "16": 0.00015, "17": 0.00015, "18": 0.00015, "19": 0.00015, "20": 0.00015, "21": 0.00015, "22": 0.00015, "23": 0.00015, "24": 0.00015, "25": 0.00015, "26": 0.00015, "27": 0.00015, "28": 0.00015, "29": 0.00015, "30": 0.00015, "31": 0.00015, "32": 0.00015, "33": 0.00015, "34": 0.00015, "35": 0.00015}, "prob_meas0_prep1": {"0": 0.0038000000000000256, "1": 0.0038000000000000256, "2": 0.0038000000000000256, "3": 0.0038000000000000256, "4": 0.0038000000000000256, "5": 0.0038000000000000256, "6": 0.0038000000000000256, "7": 0.0038000000000000256, "8": 0.0038000000000000256, "9": 0.0038000000000000256, "10": 0.0038000000000000256, "11": 0.0038000000000000256, "12": 0.0038000000000000256, "13": 0.0038000000000000256, "14": 0.0038000000000000256, "15": 0.0038000000000000256, "16": 0.0038000000000000256, "17": 0.0038000000000000256, "18": 0.0038000000000000256, "19": 0.0038000000000000256, "20": 0.0038000000000000256, "21": 0.0038000000000000256, "22": 0.0038000000000000256, "23": 0.0038000000000000256, "24": 0.0038000000000000256, "25": 0.0038000000000000256, "26": 0.0038000000000000256, "27": 0.0038000000000000256, "28": 0.0038000000000000256, "29": 0.0038000000000000256, "30": 0.0038000000000000256, "31": 0.0038000000000000256, "32": 0.0038000000000000256, "33": 0.0038000000000000256, "34": 0.0038000000000000256, "35": 0.0038000000000000256}, "prob_meas1_prep0": {"0": 0.0038000000000000256, "1": 0.0038000000000000256, "2": 0.0038000000000000256, "3": 0.0038000000000000256, "4": 0.0038000000000000256, "5": 0.0038000000000000256, "6": 0.0038000000000000256, "7": 0.0038000000000000256, "8": 0.0038000000000000256, "9": 0.0038000000000000256, "10": 0.0038000000000000256, "11": 0.0038000000000000256, "12": 0.0038000000000000256, "13": 0.0038000000000000256, "14": 0.0038000000000000256, "15": 0.0038000000000000256, "16": 0.0038000000000000256, "17": 0.0038000000000000256, "18": 0.0038000000000000256, "19": 0.0038000000000000256, "20": 0.0038000000000000256, "21": 0.0038000000000000256, "22": 0.0038000000000000256, "23": 0.0038000000000000256, "24": 0.0038000000000000256, "25": 0.0038000000000000256, "26": 0.0038000000000000256, "27": 0.0038000000000000256, "28": 0.0038000000000000256, "29": 0.0038000000000000256, "30": 0.0038000000000000256, "31": 0.0038000000000000256, "32": 0.0038000000000000256, "33": 0.0038000000000000256, "34": 0.0038000000000000256, "35": 0.0038000000000000256}, "anharmonicity": null, "gate_errs": {"rz0": 0.0, "ry0": 0.00019999999999997797, "rx0": 0.00019999999999997797, "reset0": null, "rz1": 0.0, "ry1": 0.00019999999999997797, "rx1": 0.00019999999999997797, "reset1": null, "rz2": 0.0, "ry2": 0.00019999999999997797, "rx2": 0.00019999999999997797, "reset2": null, "rz3": 0.0, "ry3": 0.00019999999999997797, "rx3": 0.00019999999999997797, "reset3": null, "rz4": 0.0, "ry4": 0.00019999999999997797, "rx4": 0.00019999999999997797, "reset4": null, "rz5": 0.0, "ry5": 0.00019999999999997797, "rx5": 0.00019999999999997797, "reset5": null, "rz6": 0.0, "ry6": 0.00019999999999997797, "rx6": 0.00019999999999997797, "reset6": null, "rz7": 0.0, "ry7": 0.00019999999999997797, "rx7": 0.00019999999999997797, "reset7": null, "rz8": 0.0, "ry8": 0.00019999999999997797, "rx8": 0.00019999999999997797, "reset8": null, "rz9": 0.0, "ry9": 0.00019999999999997797, "rx9": 0.00019999999999997797, "reset9": null, "rz10": 0.0, "ry10": 0.00019999999999997797, "rx10": 0.00019999999999997797, "reset10": null, "rz11": 0.0, "ry11": 0.00019999999999997797, "rx11": 0.00019999999999997797, "reset11": null, "rz12": 0.0, "ry12": 0.00019999999999997797, "rx12": 0.00019999999999997797, "reset12": null, "rz13": 0.0, "ry13": 0.00019999999999997797, "rx13": 0.00019999999999997797, "reset13": null, "rz14": 0.0, "ry14": 0.00019999999999997797, "rx14": 0.00019999999999997797, "reset14": null, "rz15": 0.0, "ry15": 0.00019999999999997797, "rx15": 0.00019999999999997797, "reset15": null, "rz16": 0.0, "ry16": 0.00019999999999997797, "rx16": 0.00019999999999997797, "reset16": null, "rz17": 0.0, "ry17": 0.00019999999999997797, "rx17": 0.00019999999999997797, "reset17": null, "rz18": 0.0, "ry18": 0.00019999999999997797, "rx18": 0.00019999999999997797, "reset18": null, "rz19": 0.0, "ry19": 0.00019999999999997797, "rx19": 0.00019999999999997797, "reset19": null, "rz20": 0.0, "ry20": 0.00019999999999997797, "rx20": 0.00019999999999997797, "reset20": null, "rz21": 0.0, "ry21": 0.00019999999999997797, "rx21": 0.00019999999999997797, "reset21": null, "rz22": 0.0, "ry22": 0.00019999999999997797, "rx22": 0.00019999999999997797, "reset22": null, "rz23": 0.0, "ry23": 0.00019999999999997797, "rx23": 0.00019999999999997797, "reset23": null, "rz24": 0.0, "ry24": 0.00019999999999997797, "rx24": 0.00019999999999997797, "reset24": null, "rz25": 0.0, "ry25": 0.00019999999999997797, "rx25": 0.00019999999999997797, "reset25": null, "rz26": 0.0, "ry26": 0.00019999999999997797, "rx26": 0.00019999999999997797, "reset26": null, "rz27": 0.0, "ry27": 0.00019999999999997797, "rx27": 0.00019999999999997797, "reset27": null, "rz28": 0.0, "ry28": 0.00019999999999997797, "rx28": 0.00019999999999997797, "reset28": null, "rz29": 0.0, "ry29": 0.00019999999999997797, "rx29": 0.00019999999999997797, "reset29": null, "rz30": 0.0, "ry30": 0.00019999999999997797, "rx30": 0.00019999999999997797, "reset30": null, "rz31": 0.0, "ry31": 0.00019999999999997797, "rx31": 0.00019999999999997797, "reset31": null, "rz32": 0.0, "ry32": 0.00019999999999997797, "rx32": 0.00019999999999997797, "reset32": null, "rz33": 0.0, "ry33": 0.00019999999999997797, "rx33": 0.00019999999999997797, "reset33": null, "rz34": 0.0, "ry34": 0.00019999999999997797, "rx34": 0.00019999999999997797, "reset34": null, "rz35": 0.0, "ry35": 0.00019999999999997797, "rx35": 0.00019999999999997797, "reset35": null, "rxx0_1": 0.007199999999999984, "rxx0_2": 0.007199999999999984, "rxx0_3": 0.007199999999999984, "rxx0_4": 0.007199999999999984, "rxx0_5": 0.007199999999999984, "rxx0_6": 0.007199999999999984, "rxx0_7": 0.007199999999999984, "rxx0_8": 0.007199999999999984, "rxx0_9": 0.007199999999999984, "rxx0_10": 0.007199999999999984, "rxx0_11": 0.007199999999999984, "rxx0_12": 0.007199999999999984, "rxx0_13": 0.007199999999999984, "rxx0_14": 0.007199999999999984, "rxx0_15": 0.007199999999999984, "rxx0_16": 0.007199999999999984, "rxx0_17": 0.007199999999999984, "rxx0_18": 0.007199999999999984, "rxx0_19": 0.007199999999999984, "rxx0_20": 0.007199999999999984, "rxx0_21": 0.007199999999999984, "rxx0_22": 0.007199999999999984, "rxx0_23": 0.007199999999999984, "rxx0_24": 0.007199999999999984, "rxx0_25": 0.007199999999999984, "rxx0_26": 0.007199999999999984, "rxx0_27": 0.007199999999999984, "rxx0_28": 0.007199999999999984, "rxx0_29": 0.007199999999999984, "rxx0_30": 0.007199999999999984, "rxx0_31": 0.007199999999999984, "rxx0_32": 0.007199999999999984, "rxx0_33": 0.007199999999999984, "rxx0_34": 0.007199999999999984, "rxx0_35": 0.007199999999999984, "rxx1_2": 0.007199999999999984, "rxx1_3": 0.007199999999999984, "rxx1_4": 0.007199999999999984, "rxx1_5": 0.007199999999999984, "rxx1_6": 0.007199999999999984, "rxx1_7": 0.007199999999999984, "rxx1_8": 0.007199999999999984, "rxx1_9": 0.007199999999999984, "rxx1_10": 0.007199999999999984, "rxx1_11": 0.007199999999999984, "rxx1_12": 0.007199999999999984, "rxx1_13": 0.007199999999999984, "rxx1_14": 0.007199999999999984, "rxx1_15": 0.007199999999999984, "rxx1_16": 0.007199999999999984, "rxx1_17": 0.007199999999999984, "rxx1_18": 0.007199999999999984, "rxx1_19": 0.007199999999999984, "rxx1_20": 0.007199999999999984, "rxx1_21": 0.007199999999999984, "rxx1_22": 0.007199999999999984, "rxx1_23": 0.007199999999999984, "rxx1_24": 0.007199999999999984, "rxx1_25": 0.007199999999999984, "rxx1_26": 0.007199999999999984, "rxx1_27": 0.007199999999999984, "rxx1_28": 0.007199999999999984, "rxx1_29": 0.007199999999999984, "rxx1_30": 0.007199999999999984, "rxx1_31": 0.007199999999999984, "rxx1_32": 0.007199999999999984, "rxx1_33": 0.007199999999999984, "rxx1_34": 0.007199999999999984, "rxx1_35": 0.007199999999999984, "rxx2_3": 0.007199999999999984, "rxx2_4": 0.007199999999999984, "rxx2_5": 0.007199999999999984, "rxx2_6": 0.007199999999999984, "rxx2_7": 0.007199999999999984, "rxx2_8": 0.007199999999999984, "rxx2_9": 0.007199999999999984, "rxx2_10": 0.007199999999999984, "rxx2_11": 0.007199999999999984, "rxx2_12": 0.007199999999999984, "rxx2_13": 0.007199999999999984, "rxx2_14": 0.007199999999999984, "rxx2_15": 0.007199999999999984, "rxx2_16": 0.007199999999999984, "rxx2_17": 0.007199999999999984, "rxx2_18": 0.007199999999999984, "rxx2_19": 0.007199999999999984, "rxx2_20": 0.007199999999999984, "rxx2_21": 0.007199999999999984, "rxx2_22": 0.007199999999999984, "rxx2_23": 0.007199999999999984, "rxx2_24": 0.007199999999999984, "rxx2_25": 0.007199999999999984, "rxx2_26": 0.007199999999999984, "rxx2_27": 0.007199999999999984, "rxx2_28": 0.007199999999999984, "rxx2_29": 0.007199999999999984, "rxx2_30": 0.007199999999999984, "rxx2_31": 0.007199999999999984, "rxx2_32": 0.007199999999999984, "rxx2_33": 0.007199999999999984, "rxx2_34": 0.007199999999999984, "rxx2_35": 0.007199999999999984, "rxx3_4": 0.007199999999999984, "rxx3_5": 0.007199999999999984, "rxx3_6": 0.007199999999999984, "rxx3_7": 0.007199999999999984, "rxx3_8": 0.007199999999999984, "rxx3_9": 0.007199999999999984, "rxx3_10": 0.007199999999999984, "rxx3_11": 0.007199999999999984, "rxx3_12": 0.007199999999999984, "rxx3_13": 0.007199999999999984, "rxx3_14": 0.007199999999999984, "rxx3_15": 0.007199999999999984, "rxx3_16": 0.007199999999999984, "rxx3_17": 0.007199999999999984, "rxx3_18": 0.007199999999999984, "rxx3_19": 0.007199999999999984, "rxx3_20": 0.007199999999999984, "rxx3_21": 0.007199999999999984, "rxx3_22": 0.007199999999999984, "rxx3_23": 0.007199999999999984, "rxx3_24": 0.007199999999999984, "rxx3_25": 0.007199999999999984, "rxx3_26": 0.007199999999999984, "rxx3_27": 0.007199999999999984, "rxx3_28": 0.007199999999999984, "rxx3_29": 0.007199999999999984, "rxx3_30": 0.007199999999999984, "rxx3_31": 0.007199999999999984, "rxx3_32": 0.007199999999999984, "rxx3_33": 0.007199999999999984, "rxx3_34": 0.007199999999999984, "rxx3_35": 0.007199999999999984, "rxx4_5": 0.007199999999999984, "rxx4_6": 0.007199999999999984, "rxx4_7": 0.007199999999999984, "rxx4_8": 0.007199999999999984, "rxx4_9": 0.007199999999999984, "rxx4_10": 0.007199999999999984, "rxx4_11": 0.007199999999999984, "rxx4_12": 0.007199999999999984, "rxx4_13": 0.007199999999999984, "rxx4_14": 0.007199999999999984, "rxx4_15": 0.007199999999999984, "rxx4_16": 0.007199999999999984, "rxx4_17": 0.007199999999999984, "rxx4_18": 0.007199999999999984, "rxx4_19": 0.007199999999999984, "rxx4_20": 0.007199999999999984, "rxx4_21": 0.007199999999999984, "rxx4_22": 0.007199999999999984, "rxx4_23": 0.007199999999999984, "rxx4_24": 0.007199999999999984, "rxx4_25": 0.007199999999999984, "rxx4_26": 0.007199999999999984, "rxx4_27": 0.007199999999999984, "rxx4_28": 0.007199999999999984, "rxx4_29": 0.007199999999999984, "rxx4_30": 0.007199999999999984, "rxx4_31": 0.007199999999999984, "rxx4_32": 0.007199999999999984, "rxx4_33": 0.007199999999999984, "rxx4_34": 0.007199999999999984, "rxx4_35": 0.007199999999999984, "rxx5_6": 0.007199999999999984, "rxx5_7": 0.007199999999999984, "rxx5_8": 0.007199999999999984, "rxx5_9": 0.007199999999999984, "rxx5_10": 0.007199999999999984, "rxx5_11": 0.007199999999999984, "rxx5_12": 0.007199999999999984, "rxx5_13": 0.007199999999999984, "rxx5_14": 0.007199999999999984, "rxx5_15": 0.007199999999999984, "rxx5_16": 0.007199999999999984, "rxx5_17": 0.007199999999999984, "rxx5_18": 0.007199999999999984, "rxx5_19": 0.007199999999999984, "rxx5_20": 0.007199999999999984, "rxx5_21": 0.007199999999999984, "rxx5_22": 0.007199999999999984, "rxx5_23": 0.007199999999999984, "rxx5_24": 0.007199999999999984, "rxx5_25": 0.007199999999999984, "rxx5_26": 0.007199999999999984, "rxx5_27": 0.007199999999999984, "rxx5_28": 0.007199999999999984, "rxx5_29": 0.007199999999999984, "rxx5_30": 0.007199999999999984, "rxx5_31": 0.007199999999999984, "rxx5_32": 0.007199999999999984, "rxx5_33": 0.007199999999999984, "rxx5_34": 0.007199999999999984, "rxx5_35": 0.007199999999999984, "rxx6_7": 0.007199999999999984, "rxx6_8": 0.007199999999999984, "rxx6_9": 0.007199999999999984, "rxx6_10": 0.007199999999999984, "rxx6_11": 0.007199999999999984, "rxx6_12": 0.007199999999999984, "rxx6_13": 0.007199999999999984, "rxx6_14": 0.007199999999999984, "rxx6_15": 0.007199999999999984, "rxx6_16": 0.007199999999999984, "rxx6_17": 0.007199999999999984, "rxx6_18": 0.007199999999999984, "rxx6_19": 0.007199999999999984, "rxx6_20": 0.007199999999999984, "rxx6_21": 0.007199999999999984, "rxx6_22": 0.007199999999999984, "rxx6_23": 0.007199999999999984, "rxx6_24": 0.007199999999999984, "rxx6_25": 0.007199999999999984, "rxx6_26": 0.007199999999999984, "rxx6_27": 0.007199999999999984, "rxx6_28": 0.007199999999999984, "rxx6_29": 0.007199999999999984, "rxx6_30": 0.007199999999999984, "rxx6_31": 0.007199999999999984, "rxx6_32": 0.007199999999999984, "rxx6_33": 0.007199999999999984, "rxx6_34": 0.007199999999999984, "rxx6_35": 0.007199999999999984, "rxx7_8": 0.007199999999999984, "rxx7_9": 0.007199999999999984, "rxx7_10": 0.007199999999999984, "rxx7_11": 0.007199999999999984, "rxx7_12": 0.007199999999999984, "rxx7_13": 0.007199999999999984, "rxx7_14": 0.007199999999999984, "rxx7_15": 0.007199999999999984, "rxx7_16": 0.007199999999999984, "rxx7_17": 0.007199999999999984, "rxx7_18": 0.007199999999999984, "rxx7_19": 0.007199999999999984, "rxx7_20": 0.007199999999999984, "rxx7_21": 0.007199999999999984, "rxx7_22": 0.007199999999999984, "rxx7_23": 0.007199999999999984, "rxx7_24": 0.007199999999999984, "rxx7_25": 0.007199999999999984, "rxx7_26": 0.007199999999999984, "rxx7_27": 0.007199999999999984, "rxx7_28": 0.007199999999999984, "rxx7_29": 0.007199999999999984, "rxx7_30": 0.007199999999999984, "rxx7_31": 0.007199999999999984, "rxx7_32": 0.007199999999999984, "rxx7_33": 0.007199999999999984, "rxx7_34": 0.007199999999999984, "rxx7_35": 0.007199999999999984, "rxx8_9": 0.007199999999999984, "rxx8_10": 0.007199999999999984, "rxx8_11": 0.007199999999999984, "rxx8_12": 0.007199999999999984, "rxx8_13": 0.007199999999999984, "rxx8_14": 0.007199999999999984, "rxx8_15": 0.007199999999999984, "rxx8_16": 0.007199999999999984, "rxx8_17": 0.007199999999999984, "rxx8_18": 0.007199999999999984, "rxx8_19": 0.007199999999999984, "rxx8_20": 0.007199999999999984, "rxx8_21": 0.007199999999999984, "rxx8_22": 0.007199999999999984, "rxx8_23": 0.007199999999999984, "rxx8_24": 0.007199999999999984, "rxx8_25": 0.007199999999999984, "rxx8_26": 0.007199999999999984, "rxx8_27": 0.007199999999999984, "rxx8_28": 0.007199999999999984, "rxx8_29": 0.007199999999999984, "rxx8_30": 0.007199999999999984, "rxx8_31": 0.007199999999999984, "rxx8_32": 0.007199999999999984, "rxx8_33": 0.007199999999999984, "rxx8_34": 0.007199999999999984, "rxx8_35": 0.007199999999999984, "rxx9_10": 0.007199999999999984, "rxx9_11": 0.007199999999999984, "rxx9_12": 0.007199999999999984, "rxx9_13": 0.007199999999999984, "rxx9_14": 0.007199999999999984, "rxx9_15": 0.007199999999999984, "rxx9_16": 0.007199999999999984, "rxx9_17": 0.007199999999999984, "rxx9_18": 0.007199999999999984, "rxx9_19": 0.007199999999999984, "rxx9_20": 0.007199999999999984, "rxx9_21": 0.007199999999999984, "rxx9_22": 0.007199999999999984, "rxx9_23": 0.007199999999999984, "rxx9_24": 0.007199999999999984, "rxx9_25": 0.007199999999999984, "rxx9_26": 0.007199999999999984, "rxx9_27": 0.007199999999999984, "rxx9_28": 0.007199999999999984, "rxx9_29": 0.007199999999999984, "rxx9_30": 0.007199999999999984, "rxx9_31": 0.007199999999999984, "rxx9_32": 0.007199999999999984, "rxx9_33": 0.007199999999999984, "rxx9_34": 0.007199999999999984, "rxx9_35": 0.007199999999999984, "rxx10_11": 0.007199999999999984, "rxx10_12": 0.007199999999999984, "rxx10_13": 0.007199999999999984, "rxx10_14": 0.007199999999999984, "rxx10_15": 0.007199999999999984, "rxx10_16": 0.007199999999999984, "rxx10_17": 0.007199999999999984, "rxx10_18": 0.007199999999999984, "rxx10_19": 0.007199999999999984, "rxx10_20": 0.007199999999999984, "rxx10_21": 0.007199999999999984, "rxx10_22": 0.007199999999999984, "rxx10_23": 0.007199999999999984, "rxx10_24": 0.007199999999999984, "rxx10_25": 0.007199999999999984, "rxx10_26": 0.007199999999999984, "rxx10_27": 0.007199999999999984, "rxx10_28": 0.007199999999999984, "rxx10_29": 0.007199999999999984, "rxx10_30": 0.007199999999999984, "rxx10_31": 0.007199999999999984, "rxx10_32": 0.007199999999999984, "rxx10_33": 0.007199999999999984, "rxx10_34": 0.007199999999999984, "rxx10_35": 0.007199999999999984, "rxx11_12": 0.007199999999999984, "rxx11_13": 0.007199999999999984, "rxx11_14": 0.007199999999999984, "rxx11_15": 0.007199999999999984, "rxx11_16": 0.007199999999999984, "rxx11_17": 0.007199999999999984, "rxx11_18": 0.007199999999999984, "rxx11_19": 0.007199999999999984, "rxx11_20": 0.007199999999999984, "rxx11_21": 0.007199999999999984, "rxx11_22": 0.007199999999999984, "rxx11_23": 0.007199999999999984, "rxx11_24": 0.007199999999999984, "rxx11_25": 0.007199999999999984, "rxx11_26": 0.007199999999999984, "rxx11_27": 0.007199999999999984, "rxx11_28": 0.007199999999999984, "rxx11_29": 0.007199999999999984, "rxx11_30": 0.007199999999999984, "rxx11_31": 0.007199999999999984, "rxx11_32": 0.007199999999999984, "rxx11_33": 0.007199999999999984, "rxx11_34": 0.007199999999999984, "rxx11_35": 0.007199999999999984, "rxx12_13": 0.007199999999999984, "rxx12_14": 0.007199999999999984, "rxx12_15": 0.007199999999999984, "rxx12_16": 0.007199999999999984, "rxx12_17": 0.007199999999999984, "rxx12_18": 0.007199999999999984, "rxx12_19": 0.007199999999999984, "rxx12_20": 0.007199999999999984, "rxx12_21": 0.007199999999999984, "rxx12_22": 0.007199999999999984, "rxx12_23": 0.007199999999999984, "rxx12_24": 0.007199999999999984, "rxx12_25": 0.007199999999999984, "rxx12_26": 0.007199999999999984, "rxx12_27": 0.007199999999999984, "rxx12_28": 0.007199999999999984, "rxx12_29": 0.007199999999999984, "rxx12_30": 0.007199999999999984, "rxx12_31": 0.007199999999999984, "rxx12_32": 0.007199999999999984, "rxx12_33": 0.007199999999999984, "rxx12_34": 0.007199999999999984, "rxx12_35": 0.007199999999999984, "rxx13_14": 0.007199999999999984, "rxx13_15": 0.007199999999999984, "rxx13_16": 0.007199999999999984, "rxx13_17": 0.007199999999999984, "rxx13_18": 0.007199999999999984, "rxx13_19": 0.007199999999999984, "rxx13_20": 0.007199999999999984, "rxx13_21": 0.007199999999999984, "rxx13_22": 0.007199999999999984, "rxx13_23": 0.007199999999999984, "rxx13_24": 0.007199999999999984, "rxx13_25": 0.007199999999999984, "rxx13_26": 0.007199999999999984, "rxx13_27": 0.007199999999999984, "rxx13_28": 0.007199999999999984, "rxx13_29": 0.007199999999999984, "rxx13_30": 0.007199999999999984, "rxx13_31": 0.007199999999999984, "rxx13_32": 0.007199999999999984, "rxx13_33": 0.007199999999999984, "rxx13_34": 0.007199999999999984, "rxx13_35": 0.007199999999999984, "rxx14_15": 0.007199999999999984, "rxx14_16": 0.007199999999999984, "rxx14_17": 0.007199999999999984, "rxx14_18": 0.007199999999999984, "rxx14_19": 0.007199999999999984, "rxx14_20": 0.007199999999999984, "rxx14_21": 0.007199999999999984, "rxx14_22": 0.007199999999999984, "rxx14_23": 0.007199999999999984, "rxx14_24": 0.007199999999999984, "rxx14_25": 0.007199999999999984, "rxx14_26": 0.007199999999999984, "rxx14_27": 0.007199999999999984, "rxx14_28": 0.007199999999999984, "rxx14_29": 0.007199999999999984, "rxx14_30": 0.007199999999999984, "rxx14_31": 0.007199999999999984, "rxx14_32": 0.007199999999999984, "rxx14_33": 0.007199999999999984, "rxx14_34": 0.007199999999999984, "rxx14_35": 0.007199999999999984, "rxx15_16": 0.007199999999999984, "rxx15_17": 0.007199999999999984, "rxx15_18": 0.007199999999999984, "rxx15_19": 0.007199999999999984, "rxx15_20": 0.007199999999999984, "rxx15_21": 0.007199999999999984, "rxx15_22": 0.007199999999999984, "rxx15_23": 0.007199999999999984, "rxx15_24": 0.007199999999999984, "rxx15_25": 0.007199999999999984, "rxx15_26": 0.007199999999999984, "rxx15_27": 0.007199999999999984, "rxx15_28": 0.007199999999999984, "rxx15_29": 0.007199999999999984, "rxx15_30": 0.007199999999999984, "rxx15_31": 0.007199999999999984, "rxx15_32": 0.007199999999999984, "rxx15_33": 0.007199999999999984, "rxx15_34": 0.007199999999999984, "rxx15_35": 0.007199999999999984, "rxx16_17": 0.007199999999999984, "rxx16_18": 0.007199999999999984, "rxx16_19": 0.007199999999999984, "rxx16_20": 0.007199999999999984, "rxx16_21": 0.007199999999999984, "rxx16_22": 0.007199999999999984, "rxx16_23": 0.007199999999999984, "rxx16_24": 0.007199999999999984, "rxx16_25": 0.007199999999999984, "rxx16_26": 0.007199999999999984, "rxx16_27": 0.007199999999999984, "rxx16_28": 0.007199999999999984, "rxx16_29": 0.007199999999999984, "rxx16_30": 0.007199999999999984, "rxx16_31": 0.007199999999999984, "rxx16_32": 0.007199999999999984, "rxx16_33": 0.007199999999999984, "rxx16_34": 0.007199999999999984, "rxx16_35": 0.007199999999999984, "rxx17_18": 0.007199999999999984, "rxx17_19": 0.007199999999999984, "rxx17_20": 0.007199999999999984, "rxx17_21": 0.007199999999999984, "rxx17_22": 0.007199999999999984, "rxx17_23": 0.007199999999999984, "rxx17_24": 0.007199999999999984, "rxx17_25": 0.007199999999999984, "rxx17_26": 0.007199999999999984, "rxx17_27": 0.007199999999999984, "rxx17_28": 0.007199999999999984, "rxx17_29": 0.007199999999999984, "rxx17_30": 0.007199999999999984, "rxx17_31": 0.007199999999999984, "rxx17_32": 0.007199999999999984, "rxx17_33": 0.007199999999999984, "rxx17_34": 0.007199999999999984, "rxx17_35": 0.007199999999999984, "rxx18_19": 0.007199999999999984, "rxx18_20": 0.007199999999999984, "rxx18_21": 0.007199999999999984, "rxx18_22": 0.007199999999999984, "rxx18_23": 0.007199999999999984, "rxx18_24": 0.007199999999999984, "rxx18_25": 0.007199999999999984, "rxx18_26": 0.007199999999999984, "rxx18_27": 0.007199999999999984, "rxx18_28": 0.007199999999999984, "rxx18_29": 0.007199999999999984, "rxx18_30": 0.007199999999999984, "rxx18_31": 0.007199999999999984, "rxx18_32": 0.007199999999999984, "rxx18_33": 0.007199999999999984, "rxx18_34": 0.007199999999999984, "rxx18_35": 0.007199999999999984, "rxx19_20": 0.007199999999999984, "rxx19_21": 0.007199999999999984, "rxx19_22": 0.007199999999999984, "rxx19_23": 0.007199999999999984, "rxx19_24": 0.007199999999999984, "rxx19_25": 0.007199999999999984, "rxx19_26": 0.007199999999999984, "rxx19_27": 0.007199999999999984, "rxx19_28": 0.007199999999999984, "rxx19_29": 0.007199999999999984, "rxx19_30": 0.007199999999999984, "rxx19_31": 0.007199999999999984, "rxx19_32": 0.007199999999999984, "rxx19_33": 0.007199999999999984, "rxx19_34": 0.007199999999999984, "rxx19_35": 0.007199999999999984, "rxx20_21": 0.007199999999999984, "rxx20_22": 0.007199999999999984, "rxx20_23": 0.007199999999999984, "rxx20_24": 0.007199999999999984, "rxx20_25": 0.007199999999999984, "rxx20_26": 0.007199999999999984, "rxx20_27": 0.007199999999999984, "rxx20_28": 0.007199999999999984, "rxx20_29": 0.007199999999999984, "rxx20_30": 0.007199999999999984, "rxx20_31": 0.007199999999999984, "rxx20_32": 0.007199999999999984, "rxx20_33": 0.007199999999999984, "rxx20_34": 0.007199999999999984, "rxx20_35": 0.007199999999999984, "rxx21_22": 0.007199999999999984, "rxx21_23": 0.007199999999999984, "rxx21_24": 0.007199999999999984, "rxx21_25": 0.007199999999999984, "rxx21_26": 0.007199999999999984, "rxx21_27": 0.007199999999999984, "rxx21_28": 0.007199999999999984, "rxx21_29": 0.007199999999999984, "rxx21_30": 0.007199999999999984, "rxx21_31": 0.007199999999999984, "rxx21_32": 0.007199999999999984, "rxx21_33": 0.007199999999999984, "rxx21_34": 0.007199999999999984, "rxx21_35": 0.007199999999999984, "rxx22_23": 0.007199999999999984, "rxx22_24": 0.007199999999999984, "rxx22_25": 0.007199999999999984, "rxx22_26": 0.007199999999999984, "rxx22_27": 0.007199999999999984, "rxx22_28": 0.007199999999999984, "rxx22_29": 0.007199999999999984, "rxx22_30": 0.007199999999999984, "rxx22_31": 0.007199999999999984, "rxx22_32": 0.007199999999999984, "rxx22_33": 0.007199999999999984, "rxx22_34": 0.007199999999999984, "rxx22_35": 0.007199999999999984, "rxx23_24": 0.007199999999999984, "rxx23_25": 0.007199999999999984, "rxx23_26": 0.007199999999999984, "rxx23_27": 0.007199999999999984, "rxx23_28": 0.007199999999999984, "rxx23_29": 0.007199999999999984, "rxx23_30": 0.007199999999999984, "rxx23_31": 0.007199999999999984, "rxx23_32": 0.007199999999999984, "rxx23_33": 0.007199999999999984, "rxx23_34": 0.007199999999999984, "rxx23_35": 0.007199999999999984, "rxx24_25": 0.007199999999999984, "rxx24_26": 0.007199999999999984, "rxx24_27": 0.007199999999999984, "rxx24_28": 0.007199999999999984, "rxx24_29": 0.007199999999999984, "rxx24_30": 0.007199999999999984, "rxx24_31": 0.007199999999999984, "rxx24_32": 0.007199999999999984, "rxx24_33": 0.007199999999999984, "rxx24_34": 0.007199999999999984, "rxx24_35": 0.007199999999999984, "rxx25_26": 0.007199999999999984, "rxx25_27": 0.007199999999999984, "rxx25_28": 0.007199999999999984, "rxx25_29": 0.007199999999999984, "rxx25_30": 0.007199999999999984, "rxx25_31": 0.007199999999999984, "rxx25_32": 0.007199999999999984, "rxx25_33": 0.007199999999999984, "rxx25_34": 0.007199999999999984, "rxx25_35": 0.007199999999999984, "rxx26_27": 0.007199999999999984, "rxx26_28": 0.007199999999999984, "rxx26_29": 0.007199999999999984, "rxx26_30": 0.007199999999999984, "rxx26_31": 0.007199999999999984, "rxx26_32": 0.007199999999999984, "rxx26_33": 0.007199999999999984, "rxx26_34": 0.007199999999999984, "rxx26_35": 0.007199999999999984, "rxx27_28": 0.007199999999999984, "rxx27_29": 0.007199999999999984, "rxx27_30": 0.007199999999999984, "rxx27_31": 0.007199999999999984, "rxx27_32": 0.007199999999999984, "rxx27_33": 0.007199999999999984, "rxx27_34": 0.007199999999999984, "rxx27_35": 0.007199999999999984, "rxx28_29": 0.007199999999999984, "rxx28_30": 0.007199999999999984, "rxx28_31": 0.007199999999999984, "rxx28_32": 0.007199999999999984, "rxx28_33": 0.007199999999999984, "rxx28_34": 0.007199999999999984, "rxx28_35": 0.007199999999999984, "rxx29_30": 0.007199999999999984, "rxx29_31": 0.007199999999999984, "rxx29_32": 0.007199999999999984, "rxx29_33": 0.007199999999999984, "rxx29_34": 0.007199999999999984, "rxx29_35": 0.007199999999999984, "rxx30_31": 0.007199999999999984, "rxx30_32": 0.007199999999999984, "rxx30_33": 0.007199999999999984, "rxx30_34": 0.007199999999999984, "rxx30_35": 0.007199999999999984, "rxx31_32": 0.007199999999999984, "rxx31_33": 0.007199999999999984, "rxx31_34": 0.007199999999999984, "rxx31_35": 0.007199999999999984, "rxx32_33": 0.007199999999999984, "rxx32_34": 0.007199999999999984, "rxx32_35": 0.007199999999999984, "rxx33_34": 0.007199999999999984, "rxx33_35": 0.007199999999999984, "rxx34_35": 0.007199999999999984}, "gate_lens": {"rz0": 0.0, "ry0": 0.00013, "rx0": 0.00013, "reset0": 5e-05, "rz1": 0.0, "ry1": 0.00013, "rx1": 0.00013, "reset1": 5e-05, "rz2": 0.0, "ry2": 0.00013, "rx2": 0.00013, "reset2": 5e-05, "rz3": 0.0, "ry3": 0.00013, "rx3": 0.00013, "reset3": 5e-05, "rz4": 0.0, "ry4": 0.00013, "rx4": 0.00013, "reset4": 5e-05, "rz5": 0.0, "ry5": 0.00013, "rx5": 0.00013, "reset5": 5e-05, "rz6": 0.0, "ry6": 0.00013, "rx6": 0.00013, "reset6": 5e-05, "rz7": 0.0, "ry7": 0.00013, "rx7": 0.00013, "reset7": 5e-05, "rz8": 0.0, "ry8": 0.00013, "rx8": 0.00013, "reset8": 5e-05, "rz9": 0.0, "ry9": 0.00013, "rx9": 0.00013, "reset9": 5e-05, "rz10": 0.0, "ry10": 0.00013, "rx10": 0.00013, "reset10": 5e-05, "rz11": 0.0, "ry11": 0.00013, "rx11": 0.00013, "reset11": 5e-05, "rz12": 0.0, "ry12": 0.00013, "rx12": 0.00013, "reset12": 5e-05, "rz13": 0.0, "ry13": 0.00013, "rx13": 0.00013, "reset13": 5e-05, "rz14": 0.0, "ry14": 0.00013, "rx14": 0.00013, "reset14": 5e-05, "rz15": 0.0, "ry15": 0.00013, "rx15": 0.00013, "reset15": 5e-05, "rz16": 0.0, "ry16": 0.00013, "rx16": 0.00013, "reset16": 5e-05, "rz17": 0.0, "ry17": 0.00013, "rx17": 0.00013, "reset17": 5e-05, "rz18": 0.0, "ry18": 0.00013, "rx18": 0.00013, "reset18": 5e-05, "rz19": 0.0, "ry19": 0.00013, "rx19": 0.00013, "reset19": 5e-05, "rz20": 0.0, "ry20": 0.00013, "rx20": 0.00013, "reset20": 5e-05, "rz21": 0.0, "ry21": 0.00013, "rx21": 0.00013, "reset21": 5e-05, "rz22": 0.0, "ry22": 0.00013, "rx22": 0.00013, "reset22": 5e-05, "rz23": 0.0, "ry23": 0.00013, "rx23": 0.00013, "reset23": 5e-05, "rz24": 0.0, "ry24": 0.00013, "rx24": 0.00013, "reset24": 5e-05, "rz25": 0.0, "ry25": 0.00013, "rx25": 0.00013, "reset25": 5e-05, "rz26": 0.0, "ry26": 0.00013, "rx26": 0.00013, "reset26": 5e-05, "rz27": 0.0, "ry27": 0.00013, "rx27": 0.00013, "reset27": 5e-05, "rz28": 0.0, "ry28": 0.00013, "rx28": 0.00013, "reset28": 5e-05, "rz29": 0.0, "ry29": 0.00013, "rx29": 0.00013, "reset29": 5e-05, "rz30": 0.0, "ry30": 0.00013, "rx30": 0.00013, "reset30": 5e-05, "rz31": 0.0, "ry31": 0.00013, "rx31": 0.00013, "reset31": 5e-05, "rz32": 0.0, "ry32": 0.00013, "rx32": 0.00013, "reset32": 5e-05, "rz33": 0.0, "ry33": 0.00013, "rx33": 0.00013, "reset33": 5e-05, "rz34": 0.0, "ry34": 0.00013, "rx34": 0.00013, "reset34": 5e-05, "rz35": 0.0, "ry35": 0.00013, "rx35": 0.00013, "reset35": 5e-05, "rxx0_1": 0.00097, "rxx0_2": 0.00097, "rxx0_3": 0.00097, "rxx0_4": 0.00097, "rxx0_5": 0.00097, "rxx0_6": 0.00097, "rxx0_7": 0.00097, "rxx0_8": 0.00097, "rxx0_9": 0.00097, "rxx0_10": 0.00097, "rxx0_11": 0.00097, "rxx0_12": 0.00097, "rxx0_13": 0.00097, "rxx0_14": 0.00097, "rxx0_15": 0.00097, "rxx0_16": 0.00097, "rxx0_17": 0.00097, "rxx0_18": 0.00097, "rxx0_19": 0.00097, "rxx0_20": 0.00097, "rxx0_21": 0.00097, "rxx0_22": 0.00097, "rxx0_23": 0.00097, "rxx0_24": 0.00097, "rxx0_25": 0.00097, "rxx0_26": 0.00097, "rxx0_27": 0.00097, "rxx0_28": 0.00097, "rxx0_29": 0.00097, "rxx0_30": 0.00097, "rxx0_31": 0.00097, "rxx0_32": 0.00097, "rxx0_33": 0.00097, "rxx0_34": 0.00097, "rxx0_35": 0.00097, "rxx1_2": 0.00097, "rxx1_3": 0.00097, "rxx1_4": 0.00097, "rxx1_5": 0.00097, "rxx1_6": 0.00097, "rxx1_7": 0.00097, "rxx1_8": 0.00097, "rxx1_9": 0.00097, "rxx1_10": 0.00097, "rxx1_11": 0.00097, "rxx1_12": 0.00097, "rxx1_13": 0.00097, "rxx1_14": 0.00097, "rxx1_15": 0.00097, "rxx1_16": 0.00097, "rxx1_17": 0.00097, "rxx1_18": 0.00097, "rxx1_19": 0.00097, "rxx1_20": 0.00097, "rxx1_21": 0.00097, "rxx1_22": 0.00097, "rxx1_23": 0.00097, "rxx1_24": 0.00097, "rxx1_25": 0.00097, "rxx1_26": 0.00097, "rxx1_27": 0.00097, "rxx1_28": 0.00097, "rxx1_29": 0.00097, "rxx1_30": 0.00097, "rxx1_31": 0.00097, "rxx1_32": 0.00097, "rxx1_33": 0.00097, "rxx1_34": 0.00097, "rxx1_35": 0.00097, "rxx2_3": 0.00097, "rxx2_4": 0.00097, "rxx2_5": 0.00097, "rxx2_6": 0.00097, "rxx2_7": 0.00097, "rxx2_8": 0.00097, "rxx2_9": 0.00097, "rxx2_10": 0.00097, "rxx2_11": 0.00097, "rxx2_12": 0.00097, "rxx2_13": 0.00097, "rxx2_14": 0.00097, "rxx2_15": 0.00097, "rxx2_16": 0.00097, "rxx2_17": 0.00097, "rxx2_18": 0.00097, "rxx2_19": 0.00097, "rxx2_20": 0.00097, "rxx2_21": 0.00097, "rxx2_22": 0.00097, "rxx2_23": 0.00097, "rxx2_24": 0.00097, "rxx2_25": 0.00097, "rxx2_26": 0.00097, "rxx2_27": 0.00097, "rxx2_28": 0.00097, "rxx2_29": 0.00097, "rxx2_30": 0.00097, "rxx2_31": 0.00097, "rxx2_32": 0.00097, "rxx2_33": 0.00097, "rxx2_34": 0.00097, "rxx2_35": 0.00097, "rxx3_4": 0.00097, "rxx3_5": 0.00097, "rxx3_6": 0.00097, "rxx3_7": 0.00097, "rxx3_8": 0.00097, "rxx3_9": 0.00097, "rxx3_10": 0.00097, "rxx3_11": 0.00097, "rxx3_12": 0.00097, "rxx3_13": 0.00097, "rxx3_14": 0.00097, "rxx3_15": 0.00097, "rxx3_16": 0.00097, "rxx3_17": 0.00097, "rxx3_18": 0.00097, "rxx3_19": 0.00097, "rxx3_20": 0.00097, "rxx3_21": 0.00097, "rxx3_22": 0.00097, "rxx3_23": 0.00097, "rxx3_24": 0.00097, "rxx3_25": 0.00097, "rxx3_26": 0.00097, "rxx3_27": 0.00097, "rxx3_28": 0.00097, "rxx3_29": 0.00097, "rxx3_30": 0.00097, "rxx3_31": 0.00097, "rxx3_32": 0.00097, "rxx3_33": 0.00097, "rxx3_34": 0.00097, "rxx3_35": 0.00097, "rxx4_5": 0.00097, "rxx4_6": 0.00097, "rxx4_7": 0.00097, "rxx4_8": 0.00097, "rxx4_9": 0.00097, "rxx4_10": 0.00097, "rxx4_11": 0.00097, "rxx4_12": 0.00097, "rxx4_13": 0.00097, "rxx4_14": 0.00097, "rxx4_15": 0.00097, "rxx4_16": 0.00097, "rxx4_17": 0.00097, "rxx4_18": 0.00097, "rxx4_19": 0.00097, "rxx4_20": 0.00097, "rxx4_21": 0.00097, "rxx4_22": 0.00097, "rxx4_23": 0.00097, "rxx4_24": 0.00097, "rxx4_25": 0.00097, "rxx4_26": 0.00097, "rxx4_27": 0.00097, "rxx4_28": 0.00097, "rxx4_29": 0.00097, "rxx4_30": 0.00097, "rxx4_31": 0.00097, "rxx4_32": 0.00097, "rxx4_33": 0.00097, "rxx4_34": 0.00097, "rxx4_35": 0.00097, "rxx5_6": 0.00097, "rxx5_7": 0.00097, "rxx5_8": 0.00097, "rxx5_9": 0.00097, "rxx5_10": 0.00097, "rxx5_11": 0.00097, "rxx5_12": 0.00097, "rxx5_13": 0.00097, "rxx5_14": 0.00097, "rxx5_15": 0.00097, "rxx5_16": 0.00097, "rxx5_17": 0.00097, "rxx5_18": 0.00097, "rxx5_19": 0.00097, "rxx5_20": 0.00097, "rxx5_21": 0.00097, "rxx5_22": 0.00097, "rxx5_23": 0.00097, "rxx5_24": 0.00097, "rxx5_25": 0.00097, "rxx5_26": 0.00097, "rxx5_27": 0.00097, "rxx5_28": 0.00097, "rxx5_29": 0.00097, "rxx5_30": 0.00097, "rxx5_31": 0.00097, "rxx5_32": 0.00097, "rxx5_33": 0.00097, "rxx5_34": 0.00097, "rxx5_35": 0.00097, "rxx6_7": 0.00097, "rxx6_8": 0.00097, "rxx6_9": 0.00097, "rxx6_10": 0.00097, "rxx6_11": 0.00097, "rxx6_12": 0.00097, "rxx6_13": 0.00097, "rxx6_14": 0.00097, "rxx6_15": 0.00097, "rxx6_16": 0.00097, "rxx6_17": 0.00097, "rxx6_18": 0.00097, "rxx6_19": 0.00097, "rxx6_20": 0.00097, "rxx6_21": 0.00097, "rxx6_22": 0.00097, "rxx6_23": 0.00097, "rxx6_24": 0.00097, "rxx6_25": 0.00097, "rxx6_26": 0.00097, "rxx6_27": 0.00097, "rxx6_28": 0.00097, "rxx6_29": 0.00097, "rxx6_30": 0.00097, "rxx6_31": 0.00097, "rxx6_32": 0.00097, "rxx6_33": 0.00097, "rxx6_34": 0.00097, "rxx6_35": 0.00097, "rxx7_8": 0.00097, "rxx7_9": 0.00097, "rxx7_10": 0.00097, "rxx7_11": 0.00097, "rxx7_12": 0.00097, "rxx7_13": 0.00097, "rxx7_14": 0.00097, "rxx7_15": 0.00097, "rxx7_16": 0.00097, "rxx7_17": 0.00097, "rxx7_18": 0.00097, "rxx7_19": 0.00097, "rxx7_20": 0.00097, "rxx7_21": 0.00097, "rxx7_22": 0.00097, "rxx7_23": 0.00097, "rxx7_24": 0.00097, "rxx7_25": 0.00097, "rxx7_26": 0.00097, "rxx7_27": 0.00097, "rxx7_28": 0.00097, "rxx7_29": 0.00097, "rxx7_30": 0.00097, "rxx7_31": 0.00097, "rxx7_32": 0.00097, "rxx7_33": 0.00097, "rxx7_34": 0.00097, "rxx7_35": 0.00097, "rxx8_9": 0.00097, "rxx8_10": 0.00097, "rxx8_11": 0.00097, "rxx8_12": 0.00097, "rxx8_13": 0.00097, "rxx8_14": 0.00097, "rxx8_15": 0.00097, "rxx8_16": 0.00097, "rxx8_17": 0.00097, "rxx8_18": 0.00097, "rxx8_19": 0.00097, "rxx8_20": 0.00097, "rxx8_21": 0.00097, "rxx8_22": 0.00097, "rxx8_23": 0.00097, "rxx8_24": 0.00097, "rxx8_25": 0.00097, "rxx8_26": 0.00097, "rxx8_27": 0.00097, "rxx8_28": 0.00097, "rxx8_29": 0.00097, "rxx8_30": 0.00097, "rxx8_31": 0.00097, "rxx8_32": 0.00097, "rxx8_33": 0.00097, "rxx8_34": 0.00097, "rxx8_35": 0.00097, "rxx9_10": 0.00097, "rxx9_11": 0.00097, "rxx9_12": 0.00097, "rxx9_13": 0.00097, "rxx9_14": 0.00097, "rxx9_15": 0.00097, "rxx9_16": 0.00097, "rxx9_17": 0.00097, "rxx9_18": 0.00097, "rxx9_19": 0.00097, "rxx9_20": 0.00097, "rxx9_21": 0.00097, "rxx9_22": 0.00097, "rxx9_23": 0.00097, "rxx9_24": 0.00097, "rxx9_25": 0.00097, "rxx9_26": 0.00097, "rxx9_27": 0.00097, "rxx9_28": 0.00097, "rxx9_29": 0.00097, "rxx9_30": 0.00097, "rxx9_31": 0.00097, "rxx9_32": 0.00097, "rxx9_33": 0.00097, "rxx9_34": 0.00097, "rxx9_35": 0.00097, "rxx10_11": 0.00097, "rxx10_12": 0.00097, "rxx10_13": 0.00097, "rxx10_14": 0.00097, "rxx10_15": 0.00097, "rxx10_16": 0.00097, "rxx10_17": 0.00097, "rxx10_18": 0.00097, "rxx10_19": 0.00097, "rxx10_20": 0.00097, "rxx10_21": 0.00097, "rxx10_22": 0.00097, "rxx10_23": 0.00097, "rxx10_24": 0.00097, "rxx10_25": 0.00097, "rxx10_26": 0.00097, "rxx10_27": 0.00097, "rxx10_28": 0.00097, "rxx10_29": 0.00097, "rxx10_30": 0.00097, "rxx10_31": 0.00097, "rxx10_32": 0.00097, "rxx10_33": 0.00097, "rxx10_34": 0.00097, "rxx10_35": 0.00097, "rxx11_12": 0.00097, "rxx11_13": 0.00097, "rxx11_14": 0.00097, "rxx11_15": 0.00097, "rxx11_16": 0.00097, "rxx11_17": 0.00097, "rxx11_18": 0.00097, "rxx11_19": 0.00097, "rxx11_20": 0.00097, "rxx11_21": 0.00097, "rxx11_22": 0.00097, "rxx11_23": 0.00097, "rxx11_24": 0.00097, "rxx11_25": 0.00097, "rxx11_26": 0.00097, "rxx11_27": 0.00097, "rxx11_28": 0.00097, "rxx11_29": 0.00097, "rxx11_30": 0.00097, "rxx11_31": 0.00097, "rxx11_32": 0.00097, "rxx11_33": 0.00097, "rxx11_34": 0.00097, "rxx11_35": 0.00097, "rxx12_13": 0.00097, "rxx12_14": 0.00097, "rxx12_15": 0.00097, "rxx12_16": 0.00097, "rxx12_17": 0.00097, "rxx12_18": 0.00097, "rxx12_19": 0.00097, "rxx12_20": 0.00097, "rxx12_21": 0.00097, "rxx12_22": 0.00097, "rxx12_23": 0.00097, "rxx12_24": 0.00097, "rxx12_25": 0.00097, "rxx12_26": 0.00097, "rxx12_27": 0.00097, "rxx12_28": 0.00097, "rxx12_29": 0.00097, "rxx12_30": 0.00097, "rxx12_31": 0.00097, "rxx12_32": 0.00097, "rxx12_33": 0.00097, "rxx12_34": 0.00097, "rxx12_35": 0.00097, "rxx13_14": 0.00097, "rxx13_15": 0.00097, "rxx13_16": 0.00097, "rxx13_17": 0.00097, "rxx13_18": 0.00097, "rxx13_19": 0.00097, "rxx13_20": 0.00097, "rxx13_21": 0.00097, "rxx13_22": 0.00097, "rxx13_23": 0.00097, "rxx13_24": 0.00097, "rxx13_25": 0.00097, "rxx13_26": 0.00097, "rxx13_27": 0.00097, "rxx13_28": 0.00097, "rxx13_29": 0.00097, "rxx13_30": 0.00097, "rxx13_31": 0.00097, "rxx13_32": 0.00097, "rxx13_33": 0.00097, "rxx13_34": 0.00097, "rxx13_35": 0.00097, "rxx14_15": 0.00097, "rxx14_16": 0.00097, "rxx14_17": 0.00097, "rxx14_18": 0.00097, "rxx14_19": 0.00097, "rxx14_20": 0.00097, "rxx14_21": 0.00097, "rxx14_22": 0.00097, "rxx14_23": 0.00097, "rxx14_24": 0.00097, "rxx14_25": 0.00097, "rxx14_26": 0.00097, "rxx14_27": 0.00097, "rxx14_28": 0.00097, "rxx14_29": 0.00097, "rxx14_30": 0.00097, "rxx14_31": 0.00097, "rxx14_32": 0.00097, "rxx14_33": 0.00097, "rxx14_34": 0.00097, "rxx14_35": 0.00097, "rxx15_16": 0.00097, "rxx15_17": 0.00097, "rxx15_18": 0.00097, "rxx15_19": 0.00097, "rxx15_20": 0.00097, "rxx15_21": 0.00097, "rxx15_22": 0.00097, "rxx15_23": 0.00097, "rxx15_24": 0.00097, "rxx15_25": 0.00097, "rxx15_26": 0.00097, "rxx15_27": 0.00097, "rxx15_28": 0.00097, "rxx15_29": 0.00097, "rxx15_30": 0.00097, "rxx15_31": 0.00097, "rxx15_32": 0.00097, "rxx15_33": 0.00097, "rxx15_34": 0.00097, "rxx15_35": 0.00097, "rxx16_17": 0.00097, "rxx16_18": 0.00097, "rxx16_19": 0.00097, "rxx16_20": 0.00097, "rxx16_21": 0.00097, "rxx16_22": 0.00097, "rxx16_23": 0.00097, "rxx16_24": 0.00097, "rxx16_25": 0.00097, "rxx16_26": 0.00097, "rxx16_27": 0.00097, "rxx16_28": 0.00097, "rxx16_29": 0.00097, "rxx16_30": 0.00097, "rxx16_31": 0.00097, "rxx16_32": 0.00097, "rxx16_33": 0.00097, "rxx16_34": 0.00097, "rxx16_35": 0.00097, "rxx17_18": 0.00097, "rxx17_19": 0.00097, "rxx17_20": 0.00097, "rxx17_21": 0.00097, "rxx17_22": 0.00097, "rxx17_23": 0.00097, "rxx17_24": 0.00097, "rxx17_25": 0.00097, "rxx17_26": 0.00097, "rxx17_27": 0.00097, "rxx17_28": 0.00097, "rxx17_29": 0.00097, "rxx17_30": 0.00097, "rxx17_31": 0.00097, "rxx17_32": 0.00097, "rxx17_33": 0.00097, "rxx17_34": 0.00097, "rxx17_35": 0.00097, "rxx18_19": 0.00097, "rxx18_20": 0.00097, "rxx18_21": 0.00097, "rxx18_22": 0.00097, "rxx18_23": 0.00097, "rxx18_24": 0.00097, "rxx18_25": 0.00097, "rxx18_26": 0.00097, "rxx18_27": 0.00097, "rxx18_28": 0.00097, "rxx18_29": 0.00097, "rxx18_30": 0.00097, "rxx18_31": 0.00097, "rxx18_32": 0.00097, "rxx18_33": 0.00097, "rxx18_34": 0.00097, "rxx18_35": 0.00097, "rxx19_20": 0.00097, "rxx19_21": 0.00097, "rxx19_22": 0.00097, "rxx19_23": 0.00097, "rxx19_24": 0.00097, "rxx19_25": 0.00097, "rxx19_26": 0.00097, "rxx19_27": 0.00097, "rxx19_28": 0.00097, "rxx19_29": 0.00097, "rxx19_30": 0.00097, "rxx19_31": 0.00097, "rxx19_32": 0.00097, "rxx19_33": 0.00097, "rxx19_34": 0.00097, "rxx19_35": 0.00097, "rxx20_21": 0.00097, "rxx20_22": 0.00097, "rxx20_23": 0.00097, "rxx20_24": 0.00097, "rxx20_25": 0.00097, "rxx20_26": 0.00097, "rxx20_27": 0.00097, "rxx20_28": 0.00097, "rxx20_29": 0.00097, "rxx20_30": 0.00097, "rxx20_31": 0.00097, "rxx20_32": 0.00097, "rxx20_33": 0.00097, "rxx20_34": 0.00097, "rxx20_35": 0.00097, "rxx21_22": 0.00097, "rxx21_23": 0.00097, "rxx21_24": 0.00097, "rxx21_25": 0.00097, "rxx21_26": 0.00097, "rxx21_27": 0.00097, "rxx21_28": 0.00097, "rxx21_29": 0.00097, "rxx21_30": 0.00097, "rxx21_31": 0.00097, "rxx21_32": 0.00097, "rxx21_33": 0.00097, "rxx21_34": 0.00097, "rxx21_35": 0.00097, "rxx22_23": 0.00097, "rxx22_24": 0.00097, "rxx22_25": 0.00097, "rxx22_26": 0.00097, "rxx22_27": 0.00097, "rxx22_28": 0.00097, "rxx22_29": 0.00097, "rxx22_30": 0.00097, "rxx22_31": 0.00097, "rxx22_32": 0.00097, "rxx22_33": 0.00097, "rxx22_34": 0.00097, "rxx22_35": 0.00097, "rxx23_24": 0.00097, "rxx23_25": 0.00097, "rxx23_26": 0.00097, "rxx23_27": 0.00097, "rxx23_28": 0.00097, "rxx23_29": 0.00097, "rxx23_30": 0.00097, "rxx23_31": 0.00097, "rxx23_32": 0.00097, "rxx23_33": 0.00097, "rxx23_34": 0.00097, "rxx23_35": 0.00097, "rxx24_25": 0.00097, "rxx24_26": 0.00097, "rxx24_27": 0.00097, "rxx24_28": 0.00097, "rxx24_29": 0.00097, "rxx24_30": 0.00097, "rxx24_31": 0.00097, "rxx24_32": 0.00097, "rxx24_33": 0.00097, "rxx24_34": 0.00097, "rxx24_35": 0.00097, "rxx25_26": 0.00097, "rxx25_27": 0.00097, "rxx25_28": 0.00097, "rxx25_29": 0.00097, "rxx25_30": 0.00097, "rxx25_31": 0.00097, "rxx25_32": 0.00097, "rxx25_33": 0.00097, "rxx25_34": 0.00097, "rxx25_35": 0.00097, "rxx26_27": 0.00097, "rxx26_28": 0.00097, "rxx26_29": 0.00097, "rxx26_30": 0.00097, "rxx26_31": 0.00097, "rxx26_32": 0.00097, "rxx26_33": 0.00097, "rxx26_34": 0.00097, "rxx26_35": 0.00097, "rxx27_28": 0.00097, "rxx27_29": 0.00097, "rxx27_30": 0.00097, "rxx27_31": 0.00097, "rxx27_32": 0.00097, "rxx27_33": 0.00097, "rxx27_34": 0.00097, "rxx27_35": 0.00097, "rxx28_29": 0.00097, "rxx28_30": 0.00097, "rxx28_31": 0.00097, "rxx28_32": 0.00097, "rxx28_33": 0.00097, "rxx28_34": 0.00097, "rxx28_35": 0.00097, "rxx29_30": 0.00097, "rxx29_31": 0.00097, "rxx29_32": 0.00097, "rxx29_33": 0.00097, "rxx29_34": 0.00097, "rxx29_35": 0.00097, "rxx30_31": 0.00097, "rxx30_32": 0.00097, "rxx30_33": 0.00097, "rxx30_34": 0.00097, "rxx30_35": 0.00097, "rxx31_32": 0.00097, "rxx31_33": 0.00097, "rxx31_34": 0.00097, "rxx31_35": 0.00097, "rxx32_33": 0.00097, "rxx32_34": 0.00097, "rxx32_35": 0.00097, "rxx33_34": 0.00097, "rxx33_35": 0.00097, "rxx34_35": 0.00097}, "coupling": ["0_1", "0_2", "0_3", "0_4", "0_5", "0_6", "0_7", "0_8", "0_9", "0_10", "0_11", "0_12", "0_13", "0_14", "0_15", "0_16", "0_17", "0_18", "0_19", "0_20", "0_21", "0_22", "0_23", "0_24", "0_25", "0_26", "0_27", "0_28", "0_29", "0_30", "0_31", "0_32", "0_33", "0_34", "0_35", "1_2", "1_3", "1_4", "1_5", "1_6", "1_7", "1_8", "1_9", "1_10", "1_11", "1_12", "1_13", "1_14", "1_15", "1_16", "1_17", "1_18", "1_19", "1_20", "1_21", "1_22", "1_23", "1_24", "1_25", "1_26", "1_27", "1_28", "1_29", "1_30", "1_31", "1_32", "1_33", "1_34", "1_35", "2_3", "2_4", "2_5", "2_6", "2_7", "2_8", "2_9", "2_10", "2_11", "2_12", "2_13", "2_14", "2_15", "2_16", "2_17", "2_18", "2_19", "2_20", "2_21", "2_22", "2_23", "2_24", "2_25", "2_26", "2_27", "2_28", "2_29", "2_30", "2_31", "2_32", "2_33", "2_34", "2_35", "3_4", "3_5", "3_6", "3_7", "3_8", "3_9", "3_10", "3_11", "3_12", "3_13", "3_14", "3_15", "3_16", "3_17", "3_18", "3_19", "3_20", "3_21", "3_22", "3_23", "3_24", "3_25", "3_26", "3_27", "3_28", "3_29", "3_30", "3_31", "3_32", "3_33", "3_34", "3_35", "4_5", "4_6", "4_7", "4_8", "4_9", "4_10", "4_11", "4_12", "4_13", "4_14", "4_15", "4_16", "4_17", "4_18", "4_19", "4_20", "4_21", "4_22", "4_23", "4_24", "4_25", "4_26", "4_27", "4_28", "4_29", "4_30", "4_31", "4_32", "4_33", "4_34", "4_35", "5_6", "5_7", "5_8", "5_9", "5_10", "5_11", "5_12", "5_13", "5_14", "5_15", "5_16", "5_17", "5_18", "5_19", "5_20", "5_21", "5_22", "5_23", "5_24", "5_25", "5_26", "5_27", "5_28", "5_29", "5_30", "5_31", "5_32", "5_33", "5_34", "5_35", "6_7", "6_8", "6_9", "6_10", "6_11", "6_12", "6_13", "6_14", "6_15", "6_16", "6_17", "6_18", "6_19", "6_20", "6_21", "6_22", "6_23", "6_24", "6_25", "6_26", "6_27", "6_28", "6_29", "6_30", "6_31", "6_32", "6_33", "6_34", "6_35", "7_8", "7_9", "7_10", "7_11", "7_12", "7_13", "7_14", "7_15", "7_16", "7_17", "7_18", "7_19", "7_20", "7_21", "7_22", "7_23", "7_24", "7_25", "7_26", "7_27", "7_28", "7_29", "7_30", "7_31", "7_32", "7_33", "7_34", "7_35", "8_9", "8_10", "8_11", "8_12", "8_13", "8_14", "8_15", "8_16", "8_17", "8_18", "8_19", "8_20", "8_21", "8_22", "8_23", "8_24", "8_25", "8_26", "8_27", "8_28", "8_29", "8_30", "8_31", "8_32", "8_33", "8_34", "8_35", "9_10", "9_11", "9_12", "9_13", "9_14", "9_15", "9_16", "9_17", "9_18", "9_19", "9_20", "9_21", "9_22", "9_23", "9_24", "9_25", "9_26", "9_27", "9_28", "9_29", "9_30", "9_31", "9_32", "9_33", "9_34", "9_35", "10_11", "10_12", "10_13", "10_14", "10_15", "10_16", "10_17", "10_18", "10_19", "10_20", "10_21", "10_22", "10_23", "10_24", "10_25", "10_26", "10_27", "10_28", "10_29", "10_30", "10_31", "10_32", "10_33", "10_34", "10_35", "11_12", "11_13", "11_14", "11_15", "11_16", "11_17", "11_18", "11_19", "11_20", "11_21", "11_22", "11_23", "11_24", "11_25", "11_26", "11_27", "11_28", "11_29", "11_30", "11_31", "11_32", "11_33", "11_34", "11_35", "12_13", "12_14", "12_15", "12_16", "12_17", "12_18", "12_19", "12_20", "12_21", "12_22", "12_23", "12_24", "12_25", "12_26", "12_27", "12_28", "12_29", "12_30", "12_31", "12_32", "12_33", "12_34", "12_35", "13_14", "13_15", "13_16", "13_17", "13_18", "13_19", "13_20", "13_21", "13_22", "13_23", "13_24", "13_25", "13_26", "13_27", "13_28", "13_29", "13_30", "13_31", "13_32", "13_33", "13_34", "13_35", "14_15", "14_16", "14_17", "14_18", "14_19", "14_20", "14_21", "14_22", "14_23", "14_24", "14_25", "14_26", "14_27", "14_28", "14_29", "14_30", "14_31", "14_32", "14_33", "14_34", "14_35", "15_16", "15_17", "15_18", "15_19", "15_20", "15_21", "15_22", "15_23", "15_24", "15_25", "15_26", "15_27", "15_28", "15_29", "15_30", "15_31", "15_32", "15_33", "15_34", "15_35", "16_17", "16_18", "16_19", "16_20", "16_21", "16_22", "16_23", "16_24", "16_25", "16_26", "16_27", "16_28", "16_29", "16_30", "16_31", "16_32", "16_33", "16_34", "16_35", "17_18", "17_19", "17_20", "17_21", "17_22", "17_23", "17_24", "17_25", "17_26", "17_27", "17_28", "17_29", "17_30", "17_31", "17_32", "17_33", "17_34", "17_35", "18_19", "18_20", "18_21", "18_22", "18_23", "18_24", "18_25", "18_26", "18_27", "18_28", "18_29", "18_30", "18_31", "18_32", "18_33", "18_34", "18_35", "19_20", "19_21", "19_22", "19_23", "19_24", "19_25", "19_26", "19_27", "19_28", "19_29", "19_30", "19_31", "19_32", "19_33", "19_34", "19_35", "20_21", "20_22", "20_23", "20_24", "20_25", "20_26", "20_27", "20_28", "20_29", "20_30", "20_31", "20_32", "20_33", "20_34", "20_35", "21_22", "21_23", "21_24", "21_25", "21_26", "21_27", "21_28", "21_29", "21_30", "21_31", "21_32", "21_33", "21_34", "21_35", "22_23", "22_24", "22_25", "22_26", "22_27", "22_28", "22_29", "22_30", "22_31", "22_32", "22_33", "22_34", "22_35", "23_24", "23_25", "23_26", "23_27", "23_28", "23_29", "23_30", "23_31", "23_32", "23_33", "23_34", "23_35", "24_25", "24_26", "24_27", "24_28", "24_29", "24_30", "24_31", "24_32", "24_33", "24_34", "24_35", "25_26", "25_27", "25_28", "25_29", "25_30", "25_31", "25_32", "25_33", "25_34", "25_35", "26_27", "26_28", "26_29", "26_30", "26_31", "26_32", "26_33", "26_34", "26_35", "27_28", "27_29", "27_30", "27_31", "27_32", "27_33", "27_34", "27_35", "28_29", "28_30", "28_31", "28_32", "28_33", "28_34", "28_35", "29_30", "29_31", "29_32", "29_33", "29_34", "29_35", "30_31", "30_32", "30_33", "30_34", "30_35", "31_32", "31_33", "31_34", "31_35", "32_33", "32_34", "32_35", "33_34", "33_35", "34_35"], "basis_gates": ["rxx", "rz", "ry", "rx", "reset"], "processor": {"vendor": "IonQ", "family": "Forte", "revision": "1"}, "online_date": null}